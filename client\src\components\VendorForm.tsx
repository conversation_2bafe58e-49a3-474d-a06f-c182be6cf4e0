import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  Typography,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { vendorService } from '../services/api';
import { Vendor, VendorFormData } from '../types';

interface VendorFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  vendor?: Vendor | null;
}

const VendorForm: React.FC<VendorFormProps> = ({ open, onClose, onSuccess, vendor }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<VendorFormData>({
    defaultValues: {
      name: '',
      name_en: '',
      contact_person: '',
      phone: '',
      email: '',
      address: '',
      city: '',
      country: 'السعودية',
      tax_number: '',
      commercial_register: '',
      category: '',
      rating: 0,
      status: 'نشط',
      notes: '',
    },
  });

  // تحديث النموذج عند تغيير المورد
  useEffect(() => {
    if (vendor) {
      reset({
        name: vendor.name || '',
        name_en: vendor.name_en || '',
        contact_person: vendor.contact_person || '',
        phone: vendor.phone || '',
        email: vendor.email || '',
        address: vendor.address || '',
        city: vendor.city || '',
        country: vendor.country || 'السعودية',
        tax_number: vendor.tax_number || '',
        commercial_register: vendor.commercial_register || '',
        category: vendor.category || '',
        rating: vendor.rating || 0,
        status: vendor.status || 'نشط',
        notes: vendor.notes || '',
      });
    } else {
      reset({
        name: '',
        name_en: '',
        contact_person: '',
        phone: '',
        email: '',
        address: '',
        city: '',
        country: 'السعودية',
        tax_number: '',
        commercial_register: '',
        category: '',
        rating: 0,
        status: 'نشط',
        notes: '',
      });
    }
  }, [vendor, reset]);

  // معالج إرسال النموذج
  const onSubmit = async (data: VendorFormData) => {
    try {
      setLoading(true);
      setError(null);

      let response;
      if (vendor?.id) {
        // تحديث مورد موجود
        response = await vendorService.update(vendor.id, data);
      } else {
        // إنشاء مورد جديد
        response = await vendorService.create(data);
      }

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setError(response.error || 'حدث خطأ في حفظ البيانات');
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('Error saving vendor:', err);
    } finally {
      setLoading(false);
    }
  };

  // معالج إغلاق النموذج
  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {vendor ? 'تعديل المورد' : 'إضافة مورد جديد'}
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={2}>
            {/* اسم المورد */}
            <Grid item xs={12} md={6}>
              <Controller
                name="name"
                control={control}
                rules={{ required: 'اسم المورد مطلوب' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="اسم المورد *"
                    error={!!errors.name}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>

            {/* الاسم بالإنجليزية */}
            <Grid item xs={12} md={6}>
              <Controller
                name="name_en"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="الاسم بالإنجليزية"
                  />
                )}
              />
            </Grid>

            {/* جهة الاتصال */}
            <Grid item xs={12} md={6}>
              <Controller
                name="contact_person"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="جهة الاتصال"
                  />
                )}
              />
            </Grid>

            {/* رقم الهاتف */}
            <Grid item xs={12} md={6}>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="رقم الهاتف"
                    type="tel"
                  />
                )}
              />
            </Grid>

            {/* البريد الإلكتروني */}
            <Grid item xs={12} md={6}>
              <Controller
                name="email"
                control={control}
                rules={{
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'البريد الإلكتروني غير صحيح',
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="البريد الإلكتروني"
                    type="email"
                    error={!!errors.email}
                    helperText={errors.email?.message}
                  />
                )}
              />
            </Grid>

            {/* المدينة */}
            <Grid item xs={12} md={6}>
              <Controller
                name="city"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="المدينة"
                  />
                )}
              />
            </Grid>

            {/* العنوان */}
            <Grid item xs={12}>
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="العنوان"
                    multiline
                    rows={2}
                  />
                )}
              />
            </Grid>

            {/* الرقم الضريبي */}
            <Grid item xs={12} md={6}>
              <Controller
                name="tax_number"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="الرقم الضريبي"
                  />
                )}
              />
            </Grid>

            {/* السجل التجاري */}
            <Grid item xs={12} md={6}>
              <Controller
                name="commercial_register"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="السجل التجاري"
                  />
                )}
              />
            </Grid>

            {/* الفئة */}
            <Grid item xs={12} md={6}>
              <Controller
                name="category"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>الفئة</InputLabel>
                    <Select {...field} label="الفئة">
                      <MenuItem value="مواد غذائية">مواد غذائية</MenuItem>
                      <MenuItem value="مواد بناء">مواد بناء</MenuItem>
                      <MenuItem value="معدات">معدات</MenuItem>
                      <MenuItem value="خدمات">خدمات</MenuItem>
                      <MenuItem value="تقنية">تقنية</MenuItem>
                      <MenuItem value="أخرى">أخرى</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            {/* الحالة */}
            <Grid item xs={12} md={6}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>الحالة</InputLabel>
                    <Select {...field} label="الحالة">
                      <MenuItem value="نشط">نشط</MenuItem>
                      <MenuItem value="غير نشط">غير نشط</MenuItem>
                      <MenuItem value="معلق">معلق</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            {/* التقييم */}
            <Grid item xs={12}>
              <Controller
                name="rating"
                control={control}
                render={({ field }) => (
                  <Box>
                    <Typography component="legend" gutterBottom>
                      التقييم
                    </Typography>
                    <Rating
                      {...field}
                      value={field.value || 0}
                      onChange={(_, newValue) => field.onChange(newValue || 0)}
                      max={5}
                      size="large"
                    />
                  </Box>
                )}
              />
            </Grid>

            {/* الملاحظات */}
            <Grid item xs={12}>
              <Controller
                name="notes"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="الملاحظات"
                    multiline
                    rows={3}
                  />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            إلغاء
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {vendor ? 'تحديث' : 'إضافة'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default VendorForm;
