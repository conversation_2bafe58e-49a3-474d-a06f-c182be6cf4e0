# نظام إدارة الموردين والعقود

## Vendors and Contracts Management System

نظام شامل لإدارة الموردين والعقود مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 👥 إدارة الموردين

- إضافة وتعديل بيانات الموردين
- تتبع معلومات الاتصال والتقييمات
- إدارة فئات الموردين

### 📋 إدارة العقود

- **عقود التوريد**: إدارة عقود توريد المواد والخدمات
- **عقود الإيجار**: متابعة عقود إيجار المعدات والمباني
- **اتفاقات الخصم**: إدارة اتفاقات الخصومات مع الموردين

### 🔔 نظام التنبيهات

- تنبيهات انتهاء العقود
- تذكيرات التجديد
- إشعارات المواعيد المهمة

### 📁 إدارة المستندات

- رفع وتخزين المستندات
- ربط المستندات بالعقود والموردين
- نظام أرشفة منظم

## التقنيات المستخدمة

- **Frontend**: React + TypeScript + Material-UI
- **Backend**: Node.js + Express + TypeScript
- **Database**: SQLite (قابل للترقية)
- **UI**: دعم RTL للعربية

## التثبيت والتشغيل

### 🌐 تطبيق الويب

```bash
# تثبيت جميع التبعيات
npm run install-all

# تشغيل المشروع في وضع التطوير
npm run dev
```

### 🖥️ تطبيق سطح المكتب

```bash
# تشغيل تطبيق سطح المكتب في وضع التطوير
run-desktop-dev.bat

# أو يدوياً
npm run desktop-dev

# بناء تطبيق سطح المكتب للتوزيع
build-desktop.bat

# أو يدوياً
npm run build-all
```

## هيكل المشروع

```
├── client/          # تطبيق React
├── server/          # خادم Node.js
├── electron/        # تطبيق سطح المكتب
├── database/        # ملفات قاعدة البيانات
└── docs/           # الوثائق
```

## أنواع التطبيقات

### 🌐 تطبيق الويب

- يعمل في المتصفح
- الوصول: http://localhost:3000
- مناسب للاستخدام عبر الشبكة

### 🖥️ تطبيق سطح المكتب

- تطبيق مستقل (EXE)
- لا يحتاج متصفح
- قوائم وحوارات Windows أصلية
- مثالي للتوزيع الداخلي
