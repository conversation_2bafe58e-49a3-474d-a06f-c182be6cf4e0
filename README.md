# نظام إدارة الموردين والعقود
## Vendors and Contracts Management System

نظام شامل لإدارة الموردين والعقود مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 👥 إدارة الموردين
- إضافة وتعديل بيانات الموردين
- تتبع معلومات الاتصال والتقييمات
- إدارة فئات الموردين

### 📋 إدارة العقود
- **عقود التوريد**: إدارة عقود توريد المواد والخدمات
- **عقود الإيجار**: متابعة عقود إيجار المعدات والمباني
- **اتفاقات الخصم**: إدارة اتفاقات الخصومات مع الموردين

### 🔔 نظام التنبيهات
- تنبيهات انتهاء العقود
- تذكيرات التجديد
- إشعارات المواعيد المهمة

### 📁 إدارة المستندات
- رفع وتخزين المستندات
- ربط المستندات بالعقود والموردين
- نظام أرشفة منظم

## التقنيات المستخدمة

- **Frontend**: React + TypeScript + Material-UI
- **Backend**: Node.js + Express + TypeScript
- **Database**: SQLite (قابل للترقية)
- **UI**: دعم RTL للعربية

## التثبيت والتشغيل

```bash
# تثبيت جميع التبعيات
npm run install-all

# تشغيل المشروع في وضع التطوير
npm run dev
```

## هيكل المشروع

```
├── client/          # تطبيق React
├── server/          # خادم Node.js
├── database/        # ملفات قاعدة البيانات
└── docs/           # الوثائق
```
