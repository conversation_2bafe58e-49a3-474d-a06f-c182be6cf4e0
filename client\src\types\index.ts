// أنواع البيانات للواجهة الأمامية

export interface Vendor {
  id?: number;
  name: string;
  name_en?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  tax_number?: string;
  commercial_register?: string;
  category?: string;
  rating?: number;
  status?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ContractType {
  id?: number;
  name: string;
  name_en?: string;
  description?: string;
  created_at?: string;
}

export interface Contract {
  id?: number;
  contract_number: string;
  vendor_id: number;
  contract_type_id: number;
  title: string;
  description?: string;
  start_date: string;
  end_date: string;
  value?: number;
  currency?: string;
  payment_terms?: string;
  delivery_terms?: string;
  discount_percentage?: number;
  status?: string;
  auto_renewal?: boolean;
  renewal_period?: number;
  notification_days?: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  vendor?: Vendor;
  contract_type?: ContractType;
}

export interface Document {
  id?: number;
  name: string;
  original_name: string;
  file_path: string;
  file_size?: number;
  mime_type?: string;
  contract_id?: number;
  vendor_id?: number;
  document_type?: string;
  upload_date?: string;
}

export interface Notification {
  id?: number;
  contract_id: number;
  type: string;
  title: string;
  message: string;
  notification_date: string;
  is_sent?: boolean;
  is_read?: boolean;
  created_at?: string;
  contract?: Contract;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// أنواع النماذج
export interface VendorFormData {
  name: string;
  name_en?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  tax_number?: string;
  commercial_register?: string;
  category?: string;
  rating?: number;
  status?: string;
  notes?: string;
}

export interface ContractFormData {
  contract_number?: string;
  vendor_id: number;
  contract_type_id: number;
  title: string;
  description?: string;
  start_date: string;
  end_date: string;
  value?: number;
  currency?: string;
  payment_terms?: string;
  delivery_terms?: string;
  discount_percentage?: number;
  status?: string;
  auto_renewal?: boolean;
  renewal_period?: number;
  notification_days?: number;
  notes?: string;
}

// أنواع الحالات
export type VendorStatus = 'نشط' | 'غير نشط' | 'معلق';
export type ContractStatus = 'نشط' | 'منتهي' | 'معلق' | 'ملغي';
export type NotificationType = 'انتهاء_عقد' | 'تجديد_عقد' | 'تذكير_دفع' | 'عام';
