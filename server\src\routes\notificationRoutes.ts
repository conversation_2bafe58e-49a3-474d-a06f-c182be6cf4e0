import { Router } from 'express';
import { NotificationController } from '../controllers/notificationController';

const router = Router();

// مسارات التنبيهات
router.get('/', NotificationController.getAll);                           // الحصول على جميع التنبيهات
router.get('/unread', NotificationController.getUnread);                  // الحصول على التنبيهات غير المقروءة
router.put('/read-all', NotificationController.markAllAsRead);            // تحديد جميع التنبيهات كمقروءة
router.post('/create-expiry', NotificationController.createExpiryNotifications); // إنشاء تنبيهات انتهاء العقود
router.put('/:id/read', NotificationController.markAsRead);               // تحديد تنبيه كمقروء
router.delete('/:id', NotificationController.delete);                     // حذف تنبيه

export default router;
