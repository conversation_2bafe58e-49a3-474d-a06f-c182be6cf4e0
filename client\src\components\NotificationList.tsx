import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Badge,
  Divider,
} from '@mui/material';
import {
  Notifications as NotificationIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as CheckIcon,
  Delete as DeleteIcon,
  MarkEmailRead as MarkReadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { notificationService } from '../services/api';
import { Notification } from '../types';
import moment from 'moment';

const NotificationList: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; notification: Notification | null }>({
    open: false,
    notification: null,
  });

  // تحميل التنبيهات
  const loadNotifications = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await notificationService.getAll();

      if (response.success && response.data) {
        setNotifications(response.data);
      } else {
        setError(response.error || 'حدث خطأ في تحميل التنبيهات');
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('Error loading notifications:', err);
    } finally {
      setLoading(false);
    }
  };

  // تحميل التنبيهات عند بدء التشغيل
  useEffect(() => {
    loadNotifications();
  }, []);

  // تحديد تنبيه كمقروء
  const handleMarkAsRead = async (notification: Notification) => {
    try {
      const response = await notificationService.markAsRead(notification.id!);
      
      if (response.success) {
        loadNotifications(); // إعادة تحميل القائمة
      } else {
        setError(response.error || 'حدث خطأ في تحديث التنبيه');
      }
    } catch (err) {
      setError('حدث خطأ في تحديث التنبيه');
      console.error('Error marking notification as read:', err);
    }
  };

  // تحديد جميع التنبيهات كمقروءة
  const handleMarkAllAsRead = async () => {
    try {
      const response = await notificationService.markAllAsRead();
      
      if (response.success) {
        loadNotifications(); // إعادة تحميل القائمة
      } else {
        setError(response.error || 'حدث خطأ في تحديث التنبيهات');
      }
    } catch (err) {
      setError('حدث خطأ في تحديث التنبيهات');
      console.error('Error marking all notifications as read:', err);
    }
  };

  // حذف تنبيه
  const handleDelete = async () => {
    if (!deleteDialog.notification) return;

    try {
      const response = await notificationService.delete(deleteDialog.notification.id!);
      
      if (response.success) {
        setDeleteDialog({ open: false, notification: null });
        loadNotifications(); // إعادة تحميل القائمة
      } else {
        setError(response.error || 'حدث خطأ في حذف التنبيه');
      }
    } catch (err) {
      setError('حدث خطأ في حذف التنبيه');
      console.error('Error deleting notification:', err);
    }
  };

  // الحصول على أيقونة التنبيه حسب النوع
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'انتهاء_عقد':
        return <WarningIcon color="warning" />;
      case 'تجديد_عقد':
        return <InfoIcon color="info" />;
      case 'تذكير_دفع':
        return <NotificationIcon color="primary" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  // فلترة التنبيهات حسب التبويب
  const getFilteredNotifications = () => {
    switch (tabValue) {
      case 1: // غير المقروءة
        return notifications.filter(n => !n.is_read);
      case 2: // المقروءة
        return notifications.filter(n => n.is_read);
      default: // جميع التنبيهات
        return notifications;
    }
  };

  // عدد التنبيهات غير المقروءة
  const unreadCount = notifications.filter(n => !n.is_read).length;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const filteredNotifications = getFilteredNotifications();

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          التنبيهات
          {unreadCount > 0 && (
            <Badge badgeContent={unreadCount} color="error" sx={{ ml: 2 }}>
              <NotificationIcon />
            </Badge>
          )}
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadNotifications}
            sx={{ mr: 1 }}
          >
            تحديث
          </Button>
          {unreadCount > 0 && (
            <Button
              variant="contained"
              startIcon={<MarkReadIcon />}
              onClick={handleMarkAllAsRead}
            >
              تحديد الكل كمقروء
            </Button>
          )}
        </Box>
      </Box>

      {/* التبويبات */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          variant="fullWidth"
        >
          <Tab label={`جميع التنبيهات (${notifications.length})`} />
          <Tab 
            label={
              <Box display="flex" alignItems="center">
                غير المقروءة
                {unreadCount > 0 && (
                  <Badge badgeContent={unreadCount} color="error" sx={{ ml: 1 }} />
                )}
              </Box>
            } 
          />
          <Tab label={`المقروءة (${notifications.filter(n => n.is_read).length})`} />
        </Tabs>
      </Card>

      {/* رسالة الخطأ */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* قائمة التنبيهات */}
      <Card>
        {filteredNotifications.length === 0 ? (
          <CardContent>
            <Box textAlign="center" py={4}>
              <NotificationIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                لا توجد تنبيهات
              </Typography>
            </Box>
          </CardContent>
        ) : (
          <List>
            {filteredNotifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  sx={{
                    backgroundColor: notification.is_read ? 'transparent' : 'action.hover',
                    borderRight: notification.is_read ? 'none' : '4px solid',
                    borderRightColor: 'primary.main',
                  }}
                >
                  <ListItemIcon>
                    {getNotificationIcon(notification.type)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography
                          variant="subtitle1"
                          fontWeight={notification.is_read ? 'normal' : 'bold'}
                        >
                          {notification.title}
                        </Typography>
                        <Chip
                          label={notification.type.replace('_', ' ')}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {moment(notification.notification_date).format('YYYY/MM/DD')} - 
                          {notification.contract?.contract_number} - 
                          {notification.contract?.vendor?.name}
                        </Typography>
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box display="flex" gap={1}>
                      {!notification.is_read && (
                        <IconButton
                          size="small"
                          onClick={() => handleMarkAsRead(notification)}
                          title="تحديد كمقروء"
                        >
                          <CheckIcon />
                        </IconButton>
                      )}
                      <IconButton
                        size="small"
                        onClick={() => setDeleteDialog({ open: true, notification })}
                        title="حذف"
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                
                {index < filteredNotifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Card>

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, notification: null })}
      >
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف هذا التنبيه؟
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            لا يمكن التراجع عن هذا الإجراء.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, notification: null })}>
            إلغاء
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NotificationList;
