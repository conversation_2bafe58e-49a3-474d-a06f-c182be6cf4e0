import { Router } from 'express';
import { DocumentController, upload } from '../controllers/documentController';

const router = Router();

// مسارات المستندات
router.post('/upload', upload.single('file'), DocumentController.uploadDocument);  // رفع مستند
router.get('/', DocumentController.getAll);                                        // الحصول على جميع المستندات
router.get('/contract/:contractId', DocumentController.getByContract);             // الحصول على مستندات العقد
router.get('/vendor/:vendorId', DocumentController.getByVendor);                   // الحصول على مستندات المورد
router.get('/:id/download', DocumentController.downloadDocument);                  // تحميل مستند
router.delete('/:id', DocumentController.delete);                                  // حذف مستند

export default router;
