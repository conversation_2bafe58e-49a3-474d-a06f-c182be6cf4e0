import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { contractService } from '../services/api';
import { Contract, PaginatedResponse } from '../types';
import { statusColors } from '../theme';
import moment from 'moment';

interface ContractListProps {
  onEdit?: (contract: Contract) => void;
  onView?: (contract: Contract) => void;
  onAdd?: () => void;
}

const ContractList: React.FC<ContractListProps> = ({ onEdit, onView, onAdd }) => {
  const [contracts, setContracts] = useState<PaginatedResponse<Contract>>({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; contract: Contract | null }>({
    open: false,
    contract: null,
  });

  // تحميل العقود
  const loadContracts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await contractService.getAll({
        page,
        limit: 10,
        search,
        sortBy: 'created_at',
        sortOrder: 'DESC',
      });

      if (response.success && response.data) {
        setContracts(response.data);
      } else {
        setError(response.error || 'حدث خطأ في تحميل العقود');
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('Error loading contracts:', err);
    } finally {
      setLoading(false);
    }
  };

  // تحميل العقود عند تغيير الصفحة أو البحث
  useEffect(() => {
    loadContracts();
  }, [page, search]);

  // معالج البحث
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(1);
  };

  // معالج تغيير الصفحة
  const handlePageChange = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
  };

  // معالج حذف العقد
  const handleDelete = async () => {
    if (!deleteDialog.contract) return;

    try {
      const response = await contractService.delete(deleteDialog.contract.id!);
      
      if (response.success) {
        setDeleteDialog({ open: false, contract: null });
        loadContracts();
      } else {
        setError(response.error || 'حدث خطأ في حذف العقد');
      }
    } catch (err) {
      setError('حدث خطأ في حذف العقد');
      console.error('Error deleting contract:', err);
    }
  };

  // عرض حالة العقد
  const renderStatus = (status: string = 'نشط') => {
    return (
      <Chip
        label={status}
        size="small"
        sx={{
          backgroundColor: statusColors[status as keyof typeof statusColors] || statusColors.نشط,
          color: 'white',
          fontWeight: 500,
        }}
      />
    );
  };

  // عرض تاريخ انتهاء العقد مع تحذير
  const renderEndDate = (endDate: string) => {
    const daysUntilExpiry = moment(endDate).diff(moment(), 'days');
    const isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
    const isExpired = daysUntilExpiry < 0;

    return (
      <Box display="flex" alignItems="center">
        <Typography
          variant="body2"
          color={isExpired ? 'error' : isExpiringSoon ? 'warning.main' : 'text.primary'}
        >
          {moment(endDate).format('YYYY/MM/DD')}
        </Typography>
        {(isExpiringSoon || isExpired) && (
          <WarningIcon
            sx={{
              ml: 1,
              fontSize: '1rem',
              color: isExpired ? 'error.main' : 'warning.main',
            }}
          />
        )}
      </Box>
    );
  };

  // عرض قيمة العقد
  const renderValue = (value?: number, currency?: string) => {
    if (!value) return '-';
    return `${value.toLocaleString()} ${currency || 'ريال'}`;
  };

  // فلترة العقود حسب التبويب
  const getFilteredContracts = () => {
    switch (tabValue) {
      case 1: // العقود النشطة
        return contracts.data.filter(contract => contract.status === 'نشط');
      case 2: // العقود المنتهية قريباً
        return contracts.data.filter(contract => {
          const daysUntilExpiry = moment(contract.end_date).diff(moment(), 'days');
          return daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
        });
      case 3: // العقود المنتهية
        return contracts.data.filter(contract => 
          contract.status === 'منتهي' || moment(contract.end_date).isBefore(moment())
        );
      default: // جميع العقود
        return contracts.data;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const filteredContracts = getFilteredContracts();

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          إدارة العقود
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onAdd}
          sx={{ borderRadius: 2 }}
        >
          إضافة عقد جديد
        </Button>
      </Box>

      {/* شريط البحث */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="البحث في العقود..."
            value={search}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* التبويبات */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label={`جميع العقود (${contracts.data.length})`} />
          <Tab label={`النشطة (${contracts.data.filter(c => c.status === 'نشط').length})`} />
          <Tab label={`تنتهي قريباً (${contracts.data.filter(c => {
            const days = moment(c.end_date).diff(moment(), 'days');
            return days <= 30 && days >= 0;
          }).length})`} />
          <Tab label={`المنتهية (${contracts.data.filter(c => 
            c.status === 'منتهي' || moment(c.end_date).isBefore(moment())
          ).length})`} />
        </Tabs>
      </Card>

      {/* رسالة الخطأ */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* جدول العقود */}
      <Card>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>رقم العقد</TableCell>
                <TableCell>عنوان العقد</TableCell>
                <TableCell>المورد</TableCell>
                <TableCell>نوع العقد</TableCell>
                <TableCell>تاريخ الانتهاء</TableCell>
                <TableCell>القيمة</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell align="center">الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredContracts.map((contract) => (
                <TableRow key={contract.id} hover>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="medium">
                      {contract.contract_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2">
                      {contract.title}
                    </Typography>
                    {contract.description && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {contract.description.substring(0, 50)}...
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>{contract.vendor?.name || '-'}</TableCell>
                  <TableCell>{contract.contract_type?.name || '-'}</TableCell>
                  <TableCell>{renderEndDate(contract.end_date)}</TableCell>
                  <TableCell>{renderValue(contract.value, contract.currency)}</TableCell>
                  <TableCell>{renderStatus(contract.status)}</TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => onView?.(contract)}
                      title="عرض التفاصيل"
                    >
                      <ViewIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => onEdit?.(contract)}
                      title="تعديل"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => setDeleteDialog({ open: true, contract })}
                      title="حذف"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* الترقيم */}
        {contracts.totalPages > 1 && (
          <Box display="flex" justifyContent="center" p={2}>
            <Pagination
              count={contracts.totalPages}
              page={page}
              onChange={handlePageChange}
              color="primary"
            />
          </Box>
        )}
      </Card>

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, contract: null })}
      >
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف العقد "{deleteDialog.contract?.title}"؟
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            لا يمكن التراجع عن هذا الإجراء.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, contract: null })}>
            إلغاء
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContractList;
