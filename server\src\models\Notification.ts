import { db } from '../config/database';
import { Notification } from '../types';

export class NotificationModel {
  // إنشاء تنبيه جديد
  static create(notification: Omit<Notification, 'id' | 'created_at'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO notifications (
          contract_id, type, title, message, notification_date,
          is_sent, is_read
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      
      const values = [
        notification.contract_id,
        notification.type,
        notification.title,
        notification.message,
        notification.notification_date,
        notification.is_sent || false,
        notification.is_read || false
      ];

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  // الحصول على جميع التنبيهات
  static getAll(): Promise<Notification[]> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          n.*,
          c.title as contract_title,
          c.contract_number,
          v.name as vendor_name
        FROM notifications n
        LEFT JOIN contracts c ON n.contract_id = c.id
        LEFT JOIN vendors v ON c.vendor_id = v.id
        ORDER BY n.notification_date DESC, n.created_at DESC
      `;
      
      db.all(sql, [], (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const notifications = rows.map(row => ({
            ...row,
            contract: {
              id: row.contract_id,
              title: row.contract_title,
              contract_number: row.contract_number,
              vendor: { name: row.vendor_name }
            }
          }));
          resolve(notifications);
        }
      });
    });
  }

  // الحصول على التنبيهات غير المقروءة
  static getUnread(): Promise<Notification[]> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          n.*,
          c.title as contract_title,
          c.contract_number,
          v.name as vendor_name
        FROM notifications n
        LEFT JOIN contracts c ON n.contract_id = c.id
        LEFT JOIN vendors v ON c.vendor_id = v.id
        WHERE n.is_read = 0
        ORDER BY n.notification_date DESC, n.created_at DESC
      `;
      
      db.all(sql, [], (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const notifications = rows.map(row => ({
            ...row,
            contract: {
              id: row.contract_id,
              title: row.contract_title,
              contract_number: row.contract_number,
              vendor: { name: row.vendor_name }
            }
          }));
          resolve(notifications);
        }
      });
    });
  }

  // تحديد التنبيه كمقروء
  static markAsRead(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE notifications SET is_read = 1 WHERE id = ?';
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // تحديد جميع التنبيهات كمقروءة
  static markAllAsRead(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE notifications SET is_read = 1 WHERE is_read = 0';
      
      db.run(sql, [], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(true);
        }
      });
    });
  }

  // حذف تنبيه
  static delete(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM notifications WHERE id = ?';
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // إنشاء تنبيهات انتهاء العقود
  static createExpiryNotifications(): Promise<number> {
    return new Promise((resolve, reject) => {
      // البحث عن العقود التي تنتهي خلال فترة التنبيه المحددة
      const sql = `
        SELECT 
          c.*,
          v.name as vendor_name
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        WHERE c.status = 'نشط'
        AND DATE(c.end_date) <= DATE('now', '+' || c.notification_days || ' days')
        AND c.id NOT IN (
          SELECT contract_id 
          FROM notifications 
          WHERE type = 'انتهاء_عقد' 
          AND DATE(notification_date) = DATE('now')
        )
      `;

      db.all(sql, [], (err, contracts: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        if (contracts.length === 0) {
          resolve(0);
          return;
        }

        let createdCount = 0;
        const promises = contracts.map(contract => {
          const daysUntilExpiry = Math.ceil(
            (new Date(contract.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
          );

          const notification = {
            contract_id: contract.id,
            type: 'انتهاء_عقد',
            title: `تنبيه انتهاء عقد: ${contract.title}`,
            message: `عقد "${contract.title}" مع المورد "${contract.vendor_name}" سينتهي خلال ${daysUntilExpiry} يوم (${contract.end_date})`,
            notification_date: new Date().toISOString().split('T')[0]
          };

          return this.create(notification).then(() => {
            createdCount++;
          });
        });

        Promise.all(promises)
          .then(() => resolve(createdCount))
          .catch(reject);
      });
    });
  }
}
