import { Request, Response } from 'express';
import { ContractModel } from '../models/Contract';
import { ApiResponse, Contract } from '../types';

export class ContractController {
  // إنشاء عقد جديد
  static async create(req: Request, res: Response) {
    try {
      const contractData: Omit<Contract, 'id' | 'created_at' | 'updated_at'> = req.body;
      
      // إنشاء رقم عقد تلقائي إذا لم يتم توفيره
      if (!contractData.contract_number) {
        contractData.contract_number = await ContractModel.generateContractNumber(contractData.contract_type_id);
      }
      
      const contractId = await ContractModel.create(contractData);
      const contract = await ContractModel.getById(contractId);

      const response: ApiResponse<Contract> = {
        success: true,
        data: contract!,
        message: 'تم إنشاء العقد بنجاح'
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('خطأ في إنشاء العقد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في إنشاء العقد'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على جميع العقود
  static async getAll(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string || '';
      const sortBy = req.query.sortBy as string || 'created_at';
      const sortOrder = (req.query.sortOrder as string) || 'DESC';

      const result = await ContractModel.getAll({
        page,
        limit,
        search,
        sortBy,
        sortOrder: sortOrder as 'ASC' | 'DESC'
      });

      const response: ApiResponse<typeof result> = {
        success: true,
        data: result
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب العقود:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب العقود'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على عقد بالمعرف
  static async getById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const contract = await ContractModel.getById(id);

      if (!contract) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'العقد غير موجود'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse<Contract> = {
        success: true,
        data: contract
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب العقد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب العقد'
      };
      res.status(500).json(response);
    }
  }

  // تحديث عقد
  static async update(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const contractData: Partial<Contract> = req.body;

      const updated = await ContractModel.update(id, contractData);

      if (!updated) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'العقد غير موجود أو لم يتم التحديث'
        };
        return res.status(404).json(response);
      }

      const contract = await ContractModel.getById(id);
      const response: ApiResponse<Contract> = {
        success: true,
        data: contract!,
        message: 'تم تحديث العقد بنجاح'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في تحديث العقد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في تحديث العقد'
      };
      res.status(500).json(response);
    }
  }

  // حذف عقد
  static async delete(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const deleted = await ContractModel.delete(id);

      if (!deleted) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'العقد غير موجود'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse<null> = {
        success: true,
        message: 'تم حذف العقد بنجاح'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في حذف العقد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في حذف العقد'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على العقود المنتهية قريباً
  static async getExpiring(req: Request, res: Response) {
    try {
      const days = parseInt(req.query.days as string) || 30;
      const contracts = await ContractModel.getExpiringContracts(days);

      const response: ApiResponse<Contract[]> = {
        success: true,
        data: contracts
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب العقود المنتهية:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب العقود المنتهية'
      };
      res.status(500).json(response);
    }
  }

  // إنشاء رقم عقد تلقائي
  static async generateNumber(req: Request, res: Response) {
    try {
      const { contractTypeId } = req.body;
      
      if (!contractTypeId) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'نوع العقد مطلوب'
        };
        return res.status(400).json(response);
      }

      const contractNumber = await ContractModel.generateContractNumber(contractTypeId);

      const response: ApiResponse<{ contractNumber: string }> = {
        success: true,
        data: { contractNumber }
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في إنشاء رقم العقد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في إنشاء رقم العقد'
      };
      res.status(500).json(response);
    }
  }
}
