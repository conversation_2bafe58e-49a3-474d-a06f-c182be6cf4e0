import { Request, Response } from 'express';
import { NotificationModel } from '../models/Notification';
import { ApiResponse, Notification } from '../types';

export class NotificationController {
  // الحصول على جميع التنبيهات
  static async getAll(req: Request, res: Response) {
    try {
      const notifications = await NotificationModel.getAll();

      const response: ApiResponse<Notification[]> = {
        success: true,
        data: notifications
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب التنبيهات:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب التنبيهات'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على التنبيهات غير المقروءة
  static async getUnread(req: Request, res: Response) {
    try {
      const notifications = await NotificationModel.getUnread();

      const response: ApiResponse<Notification[]> = {
        success: true,
        data: notifications
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب التنبيهات غير المقروءة:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب التنبيهات غير المقروءة'
      };
      res.status(500).json(response);
    }
  }

  // تحديد تنبيه كمقروء
  static async markAsRead(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const updated = await NotificationModel.markAsRead(id);

      if (!updated) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'التنبيه غير موجود'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse<null> = {
        success: true,
        message: 'تم تحديد التنبيه كمقروء'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في تحديث التنبيه:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في تحديث التنبيه'
      };
      res.status(500).json(response);
    }
  }

  // تحديد جميع التنبيهات كمقروءة
  static async markAllAsRead(req: Request, res: Response) {
    try {
      await NotificationModel.markAllAsRead();

      const response: ApiResponse<null> = {
        success: true,
        message: 'تم تحديد جميع التنبيهات كمقروءة'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في تحديث التنبيهات:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في تحديث التنبيهات'
      };
      res.status(500).json(response);
    }
  }

  // حذف تنبيه
  static async delete(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const deleted = await NotificationModel.delete(id);

      if (!deleted) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'التنبيه غير موجود'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse<null> = {
        success: true,
        message: 'تم حذف التنبيه بنجاح'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في حذف التنبيه:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في حذف التنبيه'
      };
      res.status(500).json(response);
    }
  }

  // إنشاء تنبيهات انتهاء العقود (يتم استدعاؤها دورياً)
  static async createExpiryNotifications(req: Request, res: Response) {
    try {
      const createdCount = await NotificationModel.createExpiryNotifications();

      const response: ApiResponse<{ count: number }> = {
        success: true,
        data: { count: createdCount },
        message: `تم إنشاء ${createdCount} تنبيه جديد`
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في إنشاء تنبيهات انتهاء العقود:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في إنشاء تنبيهات انتهاء العقود'
      };
      res.status(500).json(response);
    }
  }
}
