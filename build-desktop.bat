@echo off
echo ========================================
echo    بناء تطبيق سطح المكتب
echo    Building Desktop Application
echo ========================================
echo.

echo [1/6] تحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

echo.
echo [2/6] تثبيت تبعيات Electron...
cd electron
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات Electron
    pause
    exit /b 1
)
echo ✅ تم تثبيت تبعيات Electron

echo.
echo [3/6] بناء العميل (React)...
cd ..\client
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء العميل
    pause
    exit /b 1
)
echo ✅ تم بناء العميل

echo.
echo [4/6] بناء الخادم (Node.js)...
cd ..\server
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء الخادم
    pause
    exit /b 1
)
echo ✅ تم بناء الخادم

echo.
echo [5/6] إنشاء مجلدات البيانات...
cd ..
if not exist "database" mkdir database
if not exist "server\uploads" mkdir server\uploads
echo ✅ تم إنشاء المجلدات

echo.
echo [6/6] بناء تطبيق سطح المكتب...
cd electron
call npm run dist-win
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء التطبيق بنجاح!
echo.
echo 📁 ملفات التطبيق في: electron\dist\
echo.
echo الملفات المتاحة:
dir dist\*.exe /b 2>nul
dir dist\*.msi /b 2>nul
echo.
echo 🚀 يمكنك الآن توزيع التطبيق!
echo.
pause
