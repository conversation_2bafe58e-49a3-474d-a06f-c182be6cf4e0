import { db } from '../config/database';
import { Vendor, PaginationParams, PaginatedResponse } from '../types';

export class VendorModel {
  // إنشاء مورد جديد
  static create(vendor: Omit<Vendor, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO vendors (
          name, name_en, contact_person, phone, email, address, 
          city, country, tax_number, commercial_register, 
          category, rating, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const values = [
        vendor.name,
        vendor.name_en || null,
        vendor.contact_person || null,
        vendor.phone || null,
        vendor.email || null,
        vendor.address || null,
        vendor.city || null,
        vendor.country || 'السعودية',
        vendor.tax_number || null,
        vendor.commercial_register || null,
        vendor.category || null,
        vendor.rating || 0,
        vendor.status || 'نشط',
        vendor.notes || null
      ];

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  // الحصول على جميع الموردين مع الترقيم
  static getAll(params: PaginationParams = {}): Promise<PaginatedResponse<Vendor>> {
    return new Promise((resolve, reject) => {
      const { page = 1, limit = 10, search = '', sortBy = 'name', sortOrder = 'ASC' } = params;
      const offset = (page - 1) * limit;

      let whereClause = '';
      let searchParams: any[] = [];

      if (search) {
        whereClause = 'WHERE name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ?';
        const searchTerm = `%${search}%`;
        searchParams = [searchTerm, searchTerm, searchTerm, searchTerm];
      }

      // الحصول على العدد الإجمالي
      const countSql = `SELECT COUNT(*) as total FROM vendors ${whereClause}`;
      
      db.get(countSql, searchParams, (err, countResult: any) => {
        if (err) {
          reject(err);
          return;
        }

        const total = countResult.total;
        const totalPages = Math.ceil(total / limit);

        // الحصول على البيانات
        const dataSql = `
          SELECT * FROM vendors 
          ${whereClause}
          ORDER BY ${sortBy} ${sortOrder}
          LIMIT ? OFFSET ?
        `;

        db.all(dataSql, [...searchParams, limit, offset], (err, rows: Vendor[]) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total,
              page,
              limit,
              totalPages
            });
          }
        });
      });
    });
  }

  // الحصول على مورد بالمعرف
  static getById(id: number): Promise<Vendor | null> {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM vendors WHERE id = ?';
      
      db.get(sql, [id], (err, row: Vendor) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  // تحديث مورد
  static update(id: number, vendor: Partial<Vendor>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const fields = Object.keys(vendor).filter(key => key !== 'id' && key !== 'created_at');
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const values = fields.map(field => vendor[field as keyof Vendor]);

      const sql = `
        UPDATE vendors 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      db.run(sql, [...values, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // حذف مورد
  static delete(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM vendors WHERE id = ?';
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // الحصول على الموردين النشطين فقط
  static getActive(): Promise<Vendor[]> {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM vendors WHERE status = ? ORDER BY name';
      
      db.all(sql, ['نشط'], (err, rows: Vendor[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}
