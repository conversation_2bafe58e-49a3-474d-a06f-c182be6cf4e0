import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { vendorService } from '../services/api';
import { Vendor, PaginatedResponse } from '../types';
import { statusColors } from '../theme';

interface VendorListProps {
  onEdit?: (vendor: Vendor) => void;
  onView?: (vendor: Vendor) => void;
  onAdd?: () => void;
}

const VendorList: React.FC<VendorListProps> = ({ onEdit, onView, onAdd }) => {
  const [vendors, setVendors] = useState<PaginatedResponse<Vendor>>({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; vendor: Vendor | null }>({
    open: false,
    vendor: null,
  });

  // تحميل الموردين
  const loadVendors = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await vendorService.getAll({
        page,
        limit: 10,
        search,
        sortBy: 'name',
        sortOrder: 'ASC',
      });

      if (response.success && response.data) {
        setVendors(response.data);
      } else {
        setError(response.error || 'حدث خطأ في تحميل الموردين');
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('Error loading vendors:', err);
    } finally {
      setLoading(false);
    }
  };

  // تحميل الموردين عند تغيير الصفحة أو البحث
  useEffect(() => {
    loadVendors();
  }, [page, search]);

  // معالج البحث
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(1); // العودة للصفحة الأولى عند البحث
  };

  // معالج تغيير الصفحة
  const handlePageChange = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
  };

  // معالج حذف المورد
  const handleDelete = async () => {
    if (!deleteDialog.vendor) return;

    try {
      const response = await vendorService.delete(deleteDialog.vendor.id!);
      
      if (response.success) {
        setDeleteDialog({ open: false, vendor: null });
        loadVendors(); // إعادة تحميل القائمة
      } else {
        setError(response.error || 'حدث خطأ في حذف المورد');
      }
    } catch (err) {
      setError('حدث خطأ في حذف المورد');
      console.error('Error deleting vendor:', err);
    }
  };

  // عرض التقييم بالنجوم
  const renderRating = (rating: number = 0) => {
    return (
      <Box display="flex" alignItems="center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIcon
            key={star}
            sx={{
              color: star <= rating ? '#ffc107' : '#e0e0e0',
              fontSize: '1rem',
            }}
          />
        ))}
        <Typography variant="caption" sx={{ ml: 1 }}>
          ({rating}/5)
        </Typography>
      </Box>
    );
  };

  // عرض حالة المورد
  const renderStatus = (status: string = 'نشط') => {
    return (
      <Chip
        label={status}
        size="small"
        sx={{
          backgroundColor: statusColors[status as keyof typeof statusColors] || statusColors.نشط,
          color: 'white',
          fontWeight: 500,
        }}
      />
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          إدارة الموردين
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onAdd}
          sx={{ borderRadius: 2 }}
        >
          إضافة مورد جديد
        </Button>
      </Box>

      {/* شريط البحث */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="البحث في الموردين..."
            value={search}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* رسالة الخطأ */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* جدول الموردين */}
      <Card>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>اسم المورد</TableCell>
                <TableCell>جهة الاتصال</TableCell>
                <TableCell>الهاتف</TableCell>
                <TableCell>البريد الإلكتروني</TableCell>
                <TableCell>التقييم</TableCell>
                <TableCell>الحالة</TableCell>
                <TableCell align="center">الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {vendors.data.map((vendor) => (
                <TableRow key={vendor.id} hover>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="medium">
                      {vendor.name}
                    </Typography>
                    {vendor.name_en && (
                      <Typography variant="caption" color="text.secondary">
                        {vendor.name_en}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>{vendor.contact_person || '-'}</TableCell>
                  <TableCell>{vendor.phone || '-'}</TableCell>
                  <TableCell>{vendor.email || '-'}</TableCell>
                  <TableCell>{renderRating(vendor.rating)}</TableCell>
                  <TableCell>{renderStatus(vendor.status)}</TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => onView?.(vendor)}
                      title="عرض التفاصيل"
                    >
                      <ViewIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => onEdit?.(vendor)}
                      title="تعديل"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => setDeleteDialog({ open: true, vendor })}
                      title="حذف"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* الترقيم */}
        {vendors.totalPages > 1 && (
          <Box display="flex" justifyContent="center" p={2}>
            <Pagination
              count={vendors.totalPages}
              page={page}
              onChange={handlePageChange}
              color="primary"
            />
          </Box>
        )}
      </Card>

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, vendor: null })}
      >
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف المورد "{deleteDialog.vendor?.name}"؟
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            لا يمكن التراجع عن هذا الإجراء.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, vendor: null })}>
            إلغاء
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VendorList;
