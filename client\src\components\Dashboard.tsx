import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  People as PeopleIcon,
  Description as ContractIcon,
  Folder as DocumentIcon,
  Notifications as NotificationIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { vendorService, contractService, documentService, notificationService } from '../services/api';
import { statusColors } from '../theme';
import moment from 'moment';

interface DashboardStats {
  totalVendors: number;
  activeVendors: number;
  totalContracts: number;
  activeContracts: number;
  expiringContracts: number;
  totalDocuments: number;
  unreadNotifications: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalVendors: 0,
    activeVendors: 0,
    totalContracts: 0,
    activeContracts: 0,
    expiringContracts: 0,
    totalDocuments: 0,
    unreadNotifications: 0,
  });
  const [recentContracts, setRecentContracts] = useState<any[]>([]);
  const [expiringContracts, setExpiringContracts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // تحميل الإحصائيات
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [
        vendorsRes,
        contractsRes,
        documentsRes,
        notificationsRes,
        expiringRes,
      ] = await Promise.all([
        vendorService.getAll({ limit: 1000 }),
        contractService.getAll({ limit: 1000 }),
        documentService.getAll(),
        notificationService.getUnread(),
        contractService.getExpiring(30),
      ]);

      // حساب الإحصائيات
      const vendorsData = vendorsRes.success ? vendorsRes.data?.data || [] : [];
      const contractsData = contractsRes.success ? contractsRes.data?.data || [] : [];
      const documentsData = documentsRes.success ? documentsRes.data || [] : [];
      const notificationsData = notificationsRes.success ? notificationsRes.data || [] : [];
      const expiringData = expiringRes.success ? expiringRes.data || [] : [];

      setStats({
        totalVendors: vendorsData.length,
        activeVendors: vendorsData.filter((v: any) => v.status === 'نشط').length,
        totalContracts: contractsData.length,
        activeContracts: contractsData.filter((c: any) => c.status === 'نشط').length,
        expiringContracts: expiringData.length,
        totalDocuments: documentsData.length,
        unreadNotifications: notificationsData.length,
      });

      // أحدث العقود
      setRecentContracts(contractsData.slice(0, 5));
      
      // العقود المنتهية قريباً
      setExpiringContracts(expiringData.slice(0, 5));

    } catch (err) {
      setError('حدث خطأ في تحميل بيانات لوحة التحكم');
      console.error('Error loading dashboard data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // بطاقة إحصائية
  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, subtitle }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h4" fontWeight="bold" color={color}>
              {value.toLocaleString()}
            </Typography>
            <Typography variant="h6" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box sx={{ color, fontSize: 48 }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
        لوحة التحكم
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        نظرة عامة على نظام إدارة الموردين والعقود
      </Typography>

      {/* رسالة الخطأ */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="الموردين"
            value={stats.totalVendors}
            icon={<PeopleIcon />}
            color="primary.main"
            subtitle={`${stats.activeVendors} نشط`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="العقود"
            value={stats.totalContracts}
            icon={<ContractIcon />}
            color="success.main"
            subtitle={`${stats.activeContracts} نشط`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="المستندات"
            value={stats.totalDocuments}
            icon={<DocumentIcon />}
            color="info.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="التنبيهات"
            value={stats.unreadNotifications}
            icon={<NotificationIcon />}
            color="warning.main"
            subtitle="غير مقروءة"
          />
        </Grid>
      </Grid>

      {/* تحذير العقود المنتهية */}
      {stats.expiringContracts > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            تحذير: يوجد {stats.expiringContracts} عقد ينتهي خلال الـ 30 يوم القادمة
          </Typography>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* أحدث العقود */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                أحدث العقود
              </Typography>
              {recentContracts.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  لا توجد عقود
                </Typography>
              ) : (
                <List>
                  {recentContracts.map((contract) => (
                    <ListItem key={contract.id} divider>
                      <ListItemIcon>
                        <ContractIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={contract.title}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {contract.contract_number} - {contract.vendor_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {moment(contract.created_at).format('YYYY/MM/DD')}
                            </Typography>
                          </Box>
                        }
                      />
                      <Chip
                        label={contract.status}
                        size="small"
                        sx={{
                          backgroundColor: statusColors[contract.status as keyof typeof statusColors] || statusColors.نشط,
                          color: 'white',
                        }}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* العقود المنتهية قريباً */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                العقود المنتهية قريباً
              </Typography>
              {expiringContracts.length === 0 ? (
                <Box textAlign="center" py={2}>
                  <CheckCircleIcon sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    لا توجد عقود تنتهي قريباً
                  </Typography>
                </Box>
              ) : (
                <List>
                  {expiringContracts.map((contract) => (
                    <ListItem key={contract.id} divider>
                      <ListItemIcon>
                        <WarningIcon color="warning" />
                      </ListItemIcon>
                      <ListItemText
                        primary={contract.title}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {contract.contract_number} - {contract.vendor_name}
                            </Typography>
                            <Typography variant="caption" color="error">
                              ينتهي في: {moment(contract.end_date).format('YYYY/MM/DD')}
                              ({moment(contract.end_date).diff(moment(), 'days')} يوم)
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* مؤشر الأداء */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                مؤشرات الأداء
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">الموردين النشطين</Typography>
                      <Typography variant="body2">
                        {stats.totalVendors > 0 ? Math.round((stats.activeVendors / stats.totalVendors) * 100) : 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.totalVendors > 0 ? (stats.activeVendors / stats.totalVendors) * 100 : 0}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">العقود النشطة</Typography>
                      <Typography variant="body2">
                        {stats.totalContracts > 0 ? Math.round((stats.activeContracts / stats.totalContracts) * 100) : 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.totalContracts > 0 ? (stats.activeContracts / stats.totalContracts) * 100 : 0}
                      color="success"
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">العقود الآمنة</Typography>
                      <Typography variant="body2">
                        {stats.totalContracts > 0 ? Math.round(((stats.totalContracts - stats.expiringContracts) / stats.totalContracts) * 100) : 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.totalContracts > 0 ? ((stats.totalContracts - stats.expiringContracts) / stats.totalContracts) * 100 : 0}
                      color="info"
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
