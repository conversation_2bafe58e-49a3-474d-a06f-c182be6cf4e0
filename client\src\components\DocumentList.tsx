import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Description as FileIcon,
  Image as ImageIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { documentService, vendorService, contractService } from '../services/api';
import { Document, Vendor, Contract } from '../types';
import moment from 'moment';

const DocumentList: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadDialog, setUploadDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; document: Document | null }>({
    open: false,
    document: null,
  });
  const [uploadForm, setUploadForm] = useState({
    contractId: '',
    vendorId: '',
    documentType: 'عام',
  });

  // إعداد منطقة السحب والإفلات
  const { getRootProps, getInputProps, isDragActive, acceptedFiles } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false,
  });

  // تحميل البيانات
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [documentsRes, vendorsRes, contractsRes] = await Promise.all([
        documentService.getAll(),
        vendorService.getActive(),
        contractService.getAll({ limit: 1000 }),
      ]);

      if (documentsRes.success && documentsRes.data) {
        setDocuments(documentsRes.data);
      }

      if (vendorsRes.success && vendorsRes.data) {
        setVendors(vendorsRes.data);
      }

      if (contractsRes.success && contractsRes.data) {
        setContracts(contractsRes.data.data);
      }
    } catch (err) {
      setError('حدث خطأ في تحميل البيانات');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // رفع ملف
  const handleUpload = async () => {
    if (acceptedFiles.length === 0) {
      setError('يرجى اختيار ملف');
      return;
    }

    try {
      setUploading(true);
      setError(null);

      const file = acceptedFiles[0];
      const contractId = uploadForm.contractId ? parseInt(uploadForm.contractId) : undefined;
      const vendorId = uploadForm.vendorId ? parseInt(uploadForm.vendorId) : undefined;

      const response = await documentService.upload(
        file,
        contractId,
        vendorId,
        uploadForm.documentType
      );

      if (response.success) {
        setUploadDialog(false);
        setUploadForm({ contractId: '', vendorId: '', documentType: 'عام' });
        loadData(); // إعادة تحميل القائمة
      } else {
        setError(response.error || 'حدث خطأ في رفع الملف');
      }
    } catch (err) {
      setError('حدث خطأ في رفع الملف');
      console.error('Error uploading file:', err);
    } finally {
      setUploading(false);
    }
  };

  // تحميل ملف
  const handleDownload = async (document: Document) => {
    try {
      const blob = await documentService.download(document.id!);
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = document.original_name;
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('حدث خطأ في تحميل الملف');
      console.error('Error downloading file:', err);
    }
  };

  // حذف مستند
  const handleDelete = async () => {
    if (!deleteDialog.document) return;

    try {
      const response = await documentService.delete(deleteDialog.document.id!);

      if (response.success) {
        setDeleteDialog({ open: false, document: null });
        loadData(); // إعادة تحميل القائمة
      } else {
        setError(response.error || 'حدث خطأ في حذف المستند');
      }
    } catch (err) {
      setError('حدث خطأ في حذف المستند');
      console.error('Error deleting document:', err);
    }
  };

  // الحصول على أيقونة الملف
  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) {
      return <ImageIcon color="primary" />;
    } else if (mimeType === 'application/pdf') {
      return <PdfIcon color="error" />;
    } else {
      return <FileIcon color="action" />;
    }
  };

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          إدارة المستندات
        </Typography>
        <Button
          variant="contained"
          startIcon={<UploadIcon />}
          onClick={() => setUploadDialog(true)}
          sx={{ borderRadius: 2 }}
        >
          رفع مستند جديد
        </Button>
      </Box>

      {/* رسالة الخطأ */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* جدول المستندات */}
      <Card>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>النوع</TableCell>
                <TableCell>اسم الملف</TableCell>
                <TableCell>نوع المستند</TableCell>
                <TableCell>العقد</TableCell>
                <TableCell>المورد</TableCell>
                <TableCell>الحجم</TableCell>
                <TableCell>تاريخ الرفع</TableCell>
                <TableCell align="center">الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {documents.map((document) => (
                <TableRow key={document.id} hover>
                  <TableCell>{getFileIcon(document.mime_type || '')}</TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="medium">
                      {document.original_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip label={document.document_type || 'عام'} size="small" />
                  </TableCell>
                  <TableCell>{document.contract_id ? `عقد #${document.contract_id}` : '-'}</TableCell>
                  <TableCell>{document.vendor_id ? `مورد #${document.vendor_id}` : '-'}</TableCell>
                  <TableCell>{formatFileSize(document.file_size || 0)}</TableCell>
                  <TableCell>
                    {moment(document.upload_date).format('YYYY/MM/DD HH:mm')}
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleDownload(document)}
                      title="تحميل"
                    >
                      <DownloadIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => setDeleteDialog({ open: true, document })}
                      title="حذف"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* حوار رفع المستند */}
      <Dialog open={uploadDialog} onClose={() => setUploadDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>رفع مستند جديد</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* منطقة السحب والإفلات */}
            <Grid item xs={12}>
              <Box
                {...getRootProps()}
                sx={{
                  border: '2px dashed',
                  borderColor: isDragActive ? 'primary.main' : 'grey.300',
                  borderRadius: 2,
                  p: 3,
                  textAlign: 'center',
                  cursor: 'pointer',
                  backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
                }}
              >
                <input {...getInputProps()} />
                <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {isDragActive ? 'أفلت الملف هنا' : 'اسحب الملف هنا أو انقر للاختيار'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  الملفات المدعومة: PDF, Word, Excel, الصور (حد أقصى 10MB)
                </Typography>
                {acceptedFiles.length > 0 && (
                  <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                    تم اختيار: {acceptedFiles[0].name}
                  </Typography>
                )}
              </Box>
            </Grid>

            {/* العقد */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>العقد (اختياري)</InputLabel>
                <Select
                  value={uploadForm.contractId}
                  onChange={(e) => setUploadForm({ ...uploadForm, contractId: e.target.value })}
                  label="العقد (اختياري)"
                >
                  <MenuItem value="">بدون عقد</MenuItem>
                  {contracts.map((contract) => (
                    <MenuItem key={contract.id} value={contract.id}>
                      {contract.contract_number} - {contract.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* المورد */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>المورد (اختياري)</InputLabel>
                <Select
                  value={uploadForm.vendorId}
                  onChange={(e) => setUploadForm({ ...uploadForm, vendorId: e.target.value })}
                  label="المورد (اختياري)"
                >
                  <MenuItem value="">بدون مورد</MenuItem>
                  {vendors.map((vendor) => (
                    <MenuItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* نوع المستند */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>نوع المستند</InputLabel>
                <Select
                  value={uploadForm.documentType}
                  onChange={(e) => setUploadForm({ ...uploadForm, documentType: e.target.value })}
                  label="نوع المستند"
                >
                  <MenuItem value="عام">عام</MenuItem>
                  <MenuItem value="عقد">عقد</MenuItem>
                  <MenuItem value="فاتورة">فاتورة</MenuItem>
                  <MenuItem value="شهادة">شهادة</MenuItem>
                  <MenuItem value="تقرير">تقرير</MenuItem>
                  <MenuItem value="صورة">صورة</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialog(false)} disabled={uploading}>
            إلغاء
          </Button>
          <Button
            onClick={handleUpload}
            variant="contained"
            disabled={uploading || acceptedFiles.length === 0}
            startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
          >
            {uploading ? 'جاري الرفع...' : 'رفع'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, document: null })}
      >
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف المستند "{deleteDialog.document?.original_name}"؟
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            لا يمكن التراجع عن هذا الإجراء.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, document: null })}>
            إلغاء
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DocumentList;
