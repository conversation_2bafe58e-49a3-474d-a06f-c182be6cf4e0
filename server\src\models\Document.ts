import { db } from '../config/database';
import { Document } from '../types';

export class DocumentModel {
  // إنشاء مستند جديد
  static create(document: Omit<Document, 'id' | 'upload_date'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO documents (
          name, original_name, file_path, file_size, mime_type,
          contract_id, vendor_id, document_type
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const values = [
        document.name,
        document.original_name,
        document.file_path,
        document.file_size || null,
        document.mime_type || null,
        document.contract_id || null,
        document.vendor_id || null,
        document.document_type || null
      ];

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  // الحصول على مستندات العقد
  static getByContractId(contractId: number): Promise<Document[]> {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM documents WHERE contract_id = ? ORDER BY upload_date DESC';
      
      db.all(sql, [contractId], (err, rows: Document[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // الحصول على مستندات المورد
  static getByVendorId(vendorId: number): Promise<Document[]> {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM documents WHERE vendor_id = ? ORDER BY upload_date DESC';
      
      db.all(sql, [vendorId], (err, rows: Document[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // الحصول على مستند بالمعرف
  static getById(id: number): Promise<Document | null> {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM documents WHERE id = ?';
      
      db.get(sql, [id], (err, row: Document) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  // حذف مستند
  static delete(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM documents WHERE id = ?';
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // تحديث معلومات المستند
  static update(id: number, document: Partial<Document>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const fields = Object.keys(document).filter(key => key !== 'id' && key !== 'upload_date');
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const values = fields.map(field => document[field as keyof Document]);

      const sql = `UPDATE documents SET ${setClause} WHERE id = ?`;

      db.run(sql, [...values, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // الحصول على جميع المستندات
  static getAll(): Promise<Document[]> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          d.*,
          c.title as contract_title,
          v.name as vendor_name
        FROM documents d
        LEFT JOIN contracts c ON d.contract_id = c.id
        LEFT JOIN vendors v ON d.vendor_id = v.id
        ORDER BY d.upload_date DESC
      `;
      
      db.all(sql, [], (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}
