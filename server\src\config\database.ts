import sqlite3 from 'sqlite3';
import path from 'path';

const dbPath = path.join(__dirname, '../../../database/vendors_contracts.db');

export const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
  } else {
    console.log('تم الاتصال بقاعدة البيانات بنجاح');
  }
});

// إنشاء الجداول
export const initializeDatabase = () => {
  // جدول الموردين
  db.run(`
    CREATE TABLE IF NOT EXISTS vendors (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      name_en TEXT,
      contact_person TEXT,
      phone TEXT,
      email TEXT,
      address TEXT,
      city TEXT,
      country TEXT DEFAULT 'السعودية',
      tax_number TEXT,
      commercial_register TEXT,
      category TEXT,
      rating INTEGER DEFAULT 0,
      status TEXT DEFAULT 'نشط',
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول أنواع العقود
  db.run(`
    CREATE TABLE IF NOT EXISTS contract_types (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      name_en TEXT,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // إدراج أنواع العقود الافتراضية
  db.run(`
    INSERT OR IGNORE INTO contract_types (id, name, name_en, description) VALUES
    (1, 'عقد توريد', 'Supply Contract', 'عقود توريد المواد والخدمات'),
    (2, 'عقد إيجار', 'Rent Agreement', 'عقود إيجار المعدات والمباني'),
    (3, 'اتفاقية خصم', 'Discount Agreement', 'اتفاقيات الخصومات مع الموردين')
  `);

  // جدول العقود
  db.run(`
    CREATE TABLE IF NOT EXISTS contracts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contract_number TEXT UNIQUE NOT NULL,
      vendor_id INTEGER NOT NULL,
      contract_type_id INTEGER NOT NULL,
      title TEXT NOT NULL,
      description TEXT,
      start_date DATE NOT NULL,
      end_date DATE NOT NULL,
      value DECIMAL(15,2),
      currency TEXT DEFAULT 'ريال سعودي',
      payment_terms TEXT,
      delivery_terms TEXT,
      discount_percentage DECIMAL(5,2),
      status TEXT DEFAULT 'نشط',
      auto_renewal BOOLEAN DEFAULT 0,
      renewal_period INTEGER,
      notification_days INTEGER DEFAULT 30,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (vendor_id) REFERENCES vendors (id),
      FOREIGN KEY (contract_type_id) REFERENCES contract_types (id)
    )
  `);

  // جدول المستندات
  db.run(`
    CREATE TABLE IF NOT EXISTS documents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      original_name TEXT NOT NULL,
      file_path TEXT NOT NULL,
      file_size INTEGER,
      mime_type TEXT,
      contract_id INTEGER,
      vendor_id INTEGER,
      document_type TEXT,
      upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contract_id) REFERENCES contracts (id),
      FOREIGN KEY (vendor_id) REFERENCES vendors (id)
    )
  `);

  // جدول التنبيهات
  db.run(`
    CREATE TABLE IF NOT EXISTS notifications (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contract_id INTEGER NOT NULL,
      type TEXT NOT NULL,
      title TEXT NOT NULL,
      message TEXT NOT NULL,
      notification_date DATE NOT NULL,
      is_sent BOOLEAN DEFAULT 0,
      is_read BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contract_id) REFERENCES contracts (id)
    )
  `);

  console.log('تم إنشاء جداول قاعدة البيانات بنجاح');
};
