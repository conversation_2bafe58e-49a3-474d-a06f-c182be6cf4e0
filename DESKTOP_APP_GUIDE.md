# دليل تطبيق سطح المكتب
## نظام إدارة الموردين والعقود - Electron Desktop App

### 🖥️ نظرة عامة

تم تحويل النظام إلى تطبيق سطح مكتب مستقل باستخدام Electron.js، مما يوفر:

- **تشغيل مستقل**: بدون الحاجة لمتصفح
- **واجهة أصلية**: قوائم وحوارات Windows
- **أداء محسن**: تحكم كامل في الموارد
- **سهولة التوزيع**: ملف EXE واحد قابل للتثبيت

### 📦 أنواع التطبيقات المتاحة

#### 1. مثبت NSIS (.exe)
- مثبت كامل مع معالج التثبيت
- إنشاء اختصارات تلقائية
- إضافة/إزالة البرامج في Windows
- تحديثات تلقائية (اختياري)

#### 2. تطبيق محمول (Portable)
- ملف EXE واحد قابل للتشغيل
- لا يحتاج تثبيت
- يمكن تشغيله من USB
- مثالي للتوزيع الداخلي

### 🚀 خطوات البناء والتشغيل

#### التطوير والاختبار:

```bash
# 1. تشغيل في وضع التطوير
run-desktop-dev.bat

# أو يدوياً
cd electron
npm install
npm run dev
```

#### بناء التطبيق للإنتاج:

```bash
# بناء تلقائي كامل
build-desktop.bat

# أو يدوياً
cd electron
npm install
npm run build
npm run dist-win
```

### 📁 هيكل مجلد Electron

```
electron/
├── main.js              # العملية الرئيسية
├── preload.js           # سكريبت التحميل المسبق
├── package.json         # إعدادات Electron
├── installer.nsh       # إعدادات المثبت العربي
├── assets/             # الأيقونات والموارد
│   ├── icon.ico        # أيقونة Windows
│   ├── icon.png        # أيقونة عامة
│   └── logo.png        # شعار التطبيق
└── dist/               # ملفات التطبيق المبني
    ├── *.exe           # المثبت
    ├── *-Portable.exe  # النسخة المحمولة
    └── win-unpacked/   # ملفات التطبيق
```

### ⚙️ إعدادات التطبيق

#### الميزات المدمجة:

1. **قوائم عربية**: قوائم مترجمة بالكامل
2. **حوارات الملفات**: فتح وحفظ الملفات
3. **إشعارات النظام**: تنبيهات سطح المكتب
4. **اختصارات لوحة المفاتيح**: Ctrl+N, Ctrl+S, إلخ
5. **تحديث تلقائي**: (قابل للتفعيل)

#### إعدادات النافذة:

```javascript
{
  width: 1400,           // العرض الافتراضي
  height: 900,           // الارتفاع الافتراضي
  minWidth: 1200,        // أقل عرض
  minHeight: 700,        // أقل ارتفاع
  autoHideMenuBar: false // إظهار شريط القوائم
}
```

### 🔧 التخصيص والتطوير

#### تغيير الأيقونة:

1. ضع ملف `icon.ico` في `electron/assets/`
2. ضع ملف `icon.png` للأنظمة الأخرى
3. أعد بناء التطبيق

#### إضافة قوائم جديدة:

```javascript
// في main.js - دالة createMenu()
{
  label: 'قائمة جديدة',
  submenu: [
    {
      label: 'عنصر جديد',
      accelerator: 'CmdOrCtrl+Shift+N',
      click: () => {
        // الوظيفة هنا
      }
    }
  ]
}
```

#### إضافة حوارات مخصصة:

```javascript
// في preload.js
contextBridge.exposeInMainWorld('electronAPI', {
  showCustomDialog: () => ipcRenderer.invoke('show-custom-dialog')
});

// في main.js
ipcMain.handle('show-custom-dialog', async () => {
  const result = await dialog.showMessageBox(mainWindow, {
    type: 'question',
    buttons: ['نعم', 'لا'],
    defaultId: 0,
    message: 'هل تريد المتابعة؟'
  });
  return result;
});
```

### 📋 إعدادات البناء المتقدمة

#### تخصيص المثبت:

```json
{
  "nsis": {
    "oneClick": false,                    // مثبت تفاعلي
    "allowToChangeInstallationDirectory": true,  // اختيار مجلد التثبيت
    "createDesktopShortcut": true,        // اختصار سطح المكتب
    "createStartMenuShortcut": true,      // اختصار قائمة ابدأ
    "shortcutName": "نظام إدارة الموردين والعقود",
    "installerLanguages": ["ar", "en"]   // دعم اللغات
  }
}
```

#### إعدادات التوقيع الرقمي:

```json
{
  "win": {
    "certificateFile": "path/to/certificate.p12",
    "certificatePassword": "password",
    "signingHashAlgorithms": ["sha256"],
    "timeStampServer": "http://timestamp.digicert.com"
  }
}
```

### 🚚 التوزيع والنشر

#### للاستخدام الداخلي:

1. **النسخة المحمولة**:
   - ملف `VendorsContracts-Portable-1.0.0.exe`
   - يعمل مباشرة بدون تثبيت
   - مثالي للـ USB أو الشبكة المحلية

2. **المثبت الكامل**:
   - ملف `VendorsContracts Setup 1.0.0.exe`
   - تثبيت احترافي مع اختصارات
   - إضافة/إزالة من لوحة التحكم

#### للتوزيع الخارجي:

1. **توقيع رقمي**: ضروري لتجنب تحذيرات Windows
2. **شهادة SSL**: من جهة معتمدة
3. **اختبار شامل**: على أجهزة مختلفة

### 🔒 الأمان والحماية

#### الحماية المدمجة:

- **Context Isolation**: عزل السياق
- **Node Integration**: معطل في العارض
- **Preload Scripts**: تحكم آمن في APIs
- **CSP Headers**: حماية من XSS

#### أفضل الممارسات:

```javascript
// في main.js
webPreferences: {
  nodeIntegration: false,        // أمان
  contextIsolation: true,        // عزل
  enableRemoteModule: false,     // منع الوصول المباشر
  preload: path.join(__dirname, 'preload.js')
}
```

### 📊 مراقبة الأداء

#### مراقبة الذاكرة:

```javascript
// في main.js
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  console.log('Memory Usage:', {
    rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB'
  });
}, 30000); // كل 30 ثانية
```

### 🔄 التحديثات التلقائية

#### إعداد electron-updater:

```bash
npm install electron-updater
```

```javascript
// في main.js
const { autoUpdater } = require('electron-updater');

app.whenReady().then(() => {
  autoUpdater.checkForUpdatesAndNotify();
});
```

### 🧪 الاختبار والتصحيح

#### اختبار التطبيق:

```bash
# تشغيل في وضع التطوير
npm run dev

# بناء واختبار
npm run build
npm run start
```

#### تصحيح الأخطاء:

- **DevTools**: F12 في وضع التطوير
- **Console Logs**: في terminal الخادم
- **Error Handling**: في main.js و preload.js

### 📞 الدعم والمساعدة

#### مشاكل شائعة:

1. **فشل البناء**: تحقق من تبعيات Node.js
2. **مشاكل الأيقونة**: تأكد من وجود icon.ico
3. **مشاكل التوقيع**: تحقق من شهادة التوقيع
4. **بطء التشغيل**: تحسين حجم التطبيق

#### موارد مفيدة:

- [Electron Documentation](https://electronjs.org/docs)
- [Electron Builder](https://www.electron.build/)
- [Security Best Practices](https://electronjs.org/docs/tutorial/security)

### ✅ قائمة التحقق النهائية

- [ ] تم تثبيت تبعيات Electron
- [ ] تم بناء العميل والخادم
- [ ] تم اختبار التطبيق في وضع التطوير
- [ ] تم بناء ملفات التوزيع
- [ ] تم اختبار المثبت
- [ ] تم اختبار النسخة المحمولة
- [ ] تم إضافة الأيقونات المناسبة
- [ ] تم اختبار جميع الميزات
- [ ] التطبيق جاهز للتوزيع

🎉 **تطبيق سطح المكتب جاهز للاستخدام والتوزيع!**
