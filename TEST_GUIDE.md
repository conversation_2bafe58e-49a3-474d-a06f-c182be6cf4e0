# دليل اختبار النظام
## نظام إدارة الموردين والعقود

### قائمة الاختبارات

#### ✅ اختبار الخادم (Backend)

##### 1. اختبار تشغيل الخادم

```bash
cd server
npm run dev
```

**النتيجة المتوقعة:**
- رسالة "🚀 الخادم يعمل على المنفذ 5000"
- رسالة "تم الاتصال بقاعدة البيانات بنجاح"
- رسالة "تم إنشاء جداول قاعدة البيانات بنجاح"
- رسالة "🕘 تم تشغيل مجدول التنبيهات"

##### 2. اختبار صحة الخادم

```bash
curl http://localhost:5000/api/health
```

**النتيجة المتوقعة:**
```json
{
  "success": true,
  "message": "الخادم يعمل بشكل طبيعي",
  "timestamp": "2025-01-XX..."
}
```

##### 3. اختبار API الموردين

```bash
# إنشاء مورد جديد
curl -X POST http://localhost:5000/api/vendors \
  -H "Content-Type: application/json" \
  -d '{
    "name": "شركة الاختبار",
    "contact_person": "أحمد محمد",
    "phone": "**********",
    "email": "<EMAIL>",
    "status": "نشط"
  }'

# الحصول على جميع الموردين
curl http://localhost:5000/api/vendors
```

##### 4. اختبار API العقود

```bash
# إنشاء عقد جديد
curl -X POST http://localhost:5000/api/contracts \
  -H "Content-Type: application/json" \
  -d '{
    "vendor_id": 1,
    "contract_type_id": 1,
    "title": "عقد توريد اختبار",
    "start_date": "2025-01-01",
    "end_date": "2025-12-31",
    "value": 100000,
    "status": "نشط"
  }'

# الحصول على جميع العقود
curl http://localhost:5000/api/contracts
```

#### ✅ اختبار العميل (Frontend)

##### 1. اختبار تشغيل العميل

```bash
cd client
npm start
```

**النتيجة المتوقعة:**
- فتح المتصفح على http://localhost:3000
- عرض واجهة النظام باللغة العربية
- ظهور القائمة الجانبية والشريط العلوي

##### 2. اختبار لوحة التحكم

**الخطوات:**
1. انتقل إلى لوحة التحكم
2. تحقق من عرض الإحصائيات
3. تحقق من عرض البطاقات الإحصائية
4. تحقق من عرض مؤشرات الأداء

**النتيجة المتوقعة:**
- عرض عدد الموردين والعقود والمستندات
- عرض العقود المنتهية قريباً
- عرض أحدث العقود

##### 3. اختبار إدارة الموردين

**الخطوات:**
1. انتقل إلى صفحة الموردين
2. انقر على "إضافة مورد جديد"
3. املأ النموذج بالبيانات التالية:
   - الاسم: "شركة الاختبار"
   - جهة الاتصال: "أحمد محمد"
   - الهاتف: "**********"
   - البريد الإلكتروني: "<EMAIL>"
   - الحالة: "نشط"
4. انقر على "إضافة"
5. تحقق من ظهور المورد في القائمة
6. جرب تعديل المورد
7. جرب البحث في الموردين

**النتيجة المتوقعة:**
- إضافة المورد بنجاح
- ظهور رسالة تأكيد
- عرض المورد في الجدول
- عمل البحث والفلترة

##### 4. اختبار إدارة العقود

**الخطوات:**
1. انتقل إلى صفحة العقود
2. انقر على "إضافة عقد جديد"
3. املأ النموذج بالبيانات
4. اختر المورد المُنشأ سابقاً
5. حدد نوع العقد
6. أدخل التواريخ والقيمة
7. احفظ العقد
8. تحقق من ظهور العقد في القائمة

**النتيجة المتوقعة:**
- إنشاء رقم عقد تلقائي
- حفظ العقد بنجاح
- عرض العقد في الجدول مع بيانات المورد

##### 5. اختبار نظام التنبيهات

**الخطوات:**
1. انتقل إلى صفحة التنبيهات
2. تحقق من عرض التنبيهات
3. جرب تحديد تنبيه كمقروء
4. جرب حذف تنبيه

**النتيجة المتوقعة:**
- عرض التنبيهات بالتصنيفات
- عمل وظائف القراءة والحذف
- تحديث العدادات

##### 6. اختبار إدارة المستندات

**الخطوات:**
1. انتقل إلى صفحة المستندات
2. انقر على "رفع مستند جديد"
3. اسحب ملف PDF أو صورة
4. اختر العقد والمورد
5. حدد نوع المستند
6. ارفع الملف
7. تحقق من ظهور المستند في القائمة
8. جرب تحميل المستند

**النتيجة المتوقعة:**
- رفع الملف بنجاح
- عرض المستند في الجدول
- إمكانية تحميل الملف

#### ✅ اختبار التكامل

##### 1. اختبار تدفق العمل الكامل

**السيناريو:**
1. إنشاء مورد جديد
2. إنشاء عقد للمورد
3. رفع مستندات للعقد
4. التحقق من التنبيهات
5. عرض الإحصائيات في لوحة التحكم

##### 2. اختبار الاستجابة (Responsive Design)

**الخطوات:**
1. افتح المتصفح في وضع الهاتف المحمول
2. تحقق من عمل القائمة الجانبية
3. تحقق من عرض الجداول
4. تحقق من عمل النماذج

##### 3. اختبار المتصفحات المختلفة

**المتصفحات للاختبار:**
- Chrome
- Firefox
- Edge
- Safari (إن أمكن)

#### ✅ اختبار الأداء

##### 1. اختبار سرعة التحميل

**الأدوات:**
- Chrome DevTools
- Lighthouse

**المقاييس المطلوبة:**
- First Contentful Paint < 2s
- Largest Contentful Paint < 4s
- Cumulative Layout Shift < 0.1

##### 2. اختبار الذاكرة

**الخطوات:**
1. افتح Chrome DevTools
2. انتقل إلى تبويب Memory
3. راقب استخدام الذاكرة أثناء التنقل
4. تحقق من عدم وجود تسريبات في الذاكرة

#### ✅ اختبار الأمان

##### 1. اختبار رفع الملفات

**الخطوات:**
1. جرب رفع ملف بحجم كبير (>10MB)
2. جرب رفع ملف بنوع غير مدعوم
3. تحقق من رسائل الخطأ المناسبة

##### 2. اختبار التحقق من البيانات

**الخطوات:**
1. جرب إرسال بيانات فارغة
2. جرب إرسال بريد إلكتروني غير صحيح
3. تحقق من رسائل التحقق

#### ✅ قائمة التحقق النهائية

- [ ] الخادم يعمل بدون أخطاء
- [ ] العميل يعمل بدون أخطاء
- [ ] قاعدة البيانات تُنشأ تلقائياً
- [ ] جميع صفحات النظام تعمل
- [ ] إضافة وتعديل وحذف الموردين
- [ ] إضافة وتعديل وحذف العقود
- [ ] رفع وتحميل المستندات
- [ ] عرض وإدارة التنبيهات
- [ ] لوحة التحكم تعرض الإحصائيات
- [ ] البحث والفلترة تعمل
- [ ] التصميم متجاوب مع الأجهزة المختلفة
- [ ] النظام يدعم اللغة العربية بشكل كامل
- [ ] المجدول اليومي للتنبيهات يعمل

### تقرير الاختبار

بعد إجراء جميع الاختبارات، املأ التقرير التالي:

#### النتائج:
- ✅ جميع الاختبارات نجحت
- ⚠️ بعض الاختبارات تحتاج تحسين
- ❌ يوجد أخطاء تحتاج إصلاح

#### الملاحظات:
- [اكتب أي ملاحظات أو مشاكل واجهتها]

#### التوصيات:
- [اكتب أي تحسينات مقترحة]
