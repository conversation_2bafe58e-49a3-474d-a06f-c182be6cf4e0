{"name": "vendors-contracts-management", "version": "1.0.0", "description": "نظام إدارة الموردين والعقود - Vendors and Contracts Management System", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["vendors", "contracts", "management", "arabic", "موردين", "<PERSON><PERSON>و<PERSON>"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}