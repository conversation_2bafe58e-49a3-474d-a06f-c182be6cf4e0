import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { DocumentModel } from '../models/Document';
import { ApiResponse, Document } from '../types';

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    
    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // إنشاء اسم ملف فريد
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, `document-${uniqueSuffix}${extension}`);
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // قائمة الملفات المسموحة
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/gif'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('نوع الملف غير مدعوم'));
  }
};

export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  }
});

export class DocumentController {
  // رفع مستند جديد
  static async uploadDocument(req: Request, res: Response) {
    try {
      if (!req.file) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'لم يتم اختيار ملف'
        };
        return res.status(400).json(response);
      }

      const { contractId, vendorId, documentType } = req.body;

      const documentData = {
        name: req.file.filename,
        original_name: req.file.originalname,
        file_path: req.file.path,
        file_size: req.file.size,
        mime_type: req.file.mimetype,
        contract_id: contractId ? parseInt(contractId) : undefined,
        vendor_id: vendorId ? parseInt(vendorId) : undefined,
        document_type: documentType || 'عام'
      };

      const documentId = await DocumentModel.create(documentData);
      const document = await DocumentModel.getById(documentId);

      const response: ApiResponse<Document> = {
        success: true,
        data: document!,
        message: 'تم رفع المستند بنجاح'
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('خطأ في رفع المستند:', error);
      
      // حذف الملف في حالة الخطأ
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في رفع المستند'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على مستندات العقد
  static async getByContract(req: Request, res: Response) {
    try {
      const contractId = parseInt(req.params.contractId);
      const documents = await DocumentModel.getByContractId(contractId);

      const response: ApiResponse<Document[]> = {
        success: true,
        data: documents
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب مستندات العقد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب مستندات العقد'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على مستندات المورد
  static async getByVendor(req: Request, res: Response) {
    try {
      const vendorId = parseInt(req.params.vendorId);
      const documents = await DocumentModel.getByVendorId(vendorId);

      const response: ApiResponse<Document[]> = {
        success: true,
        data: documents
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب مستندات المورد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب مستندات المورد'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على جميع المستندات
  static async getAll(req: Request, res: Response) {
    try {
      const documents = await DocumentModel.getAll();

      const response: ApiResponse<Document[]> = {
        success: true,
        data: documents
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب المستندات:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب المستندات'
      };
      res.status(500).json(response);
    }
  }

  // تحميل مستند
  static async downloadDocument(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const document = await DocumentModel.getById(id);

      if (!document) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'المستند غير موجود'
        };
        return res.status(404).json(response);
      }

      if (!fs.existsSync(document.file_path)) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'ملف المستند غير موجود'
        };
        return res.status(404).json(response);
      }

      res.download(document.file_path, document.original_name);
    } catch (error) {
      console.error('خطأ في تحميل المستند:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في تحميل المستند'
      };
      res.status(500).json(response);
    }
  }

  // حذف مستند
  static async delete(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const document = await DocumentModel.getById(id);

      if (!document) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'المستند غير موجود'
        };
        return res.status(404).json(response);
      }

      // حذف الملف من النظام
      if (fs.existsSync(document.file_path)) {
        fs.unlinkSync(document.file_path);
      }

      // حذف السجل من قاعدة البيانات
      const deleted = await DocumentModel.delete(id);

      if (!deleted) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'فشل في حذف المستند'
        };
        return res.status(500).json(response);
      }

      const response: ApiResponse<null> = {
        success: true,
        message: 'تم حذف المستند بنجاح'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في حذف المستند:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في حذف المستند'
      };
      res.status(500).json(response);
    }
  }
}
