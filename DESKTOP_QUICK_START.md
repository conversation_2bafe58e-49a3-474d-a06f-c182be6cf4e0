# 🚀 تشغيل سريع - تطبيق سطح المكتب

## ⚡ التشغيل السريع

### 1. تشغيل في وضع التطوير:
```bash
# الطريقة السريعة
run-desktop-dev.bat

# أو يدوياً
cd electron
npm install
npm run dev
```

### 2. بناء التطبيق للتوزيع:
```bash
# الطريقة السريعة
build-desktop.bat

# أو يدوياً
npm run build-all
```

## 📦 ما ستحصل عليه

### ملفات التطبيق في `electron/dist/`:

#### 1. المثبت الكامل:
- `VendorsContracts Setup 1.0.0.exe`
- مثبت احترافي مع معالج التثبيت
- إنشاء اختصارات تلقائية
- إضافة للبرامج المثبتة

#### 2. النسخة المحمولة:
- `VendorsContracts-Portable-1.0.0.exe`
- تشغيل مباشر بدون تثبيت
- مثالي للـ USB أو الشبكة

#### 3. ملفات التطبيق:
- مجلد `win-unpacked/`
- ملفات التطبيق غير المضغوطة
- للتطوير والاختبار

## 🎯 الاستخدام

### تشغيل التطبيق:
1. شغّل ملف EXE
2. ستظهر نافذة التطبيق
3. النظام يعمل بدون متصفح
4. جميع الميزات متاحة

### الميزات المتاحة:
- ✅ إدارة الموردين
- ✅ إدارة العقود  
- ✅ رفع المستندات
- ✅ نظام التنبيهات
- ✅ لوحة التحكم
- ✅ قوائم عربية
- ✅ اختصارات لوحة المفاتيح

## 🔧 التخصيص

### إضافة أيقونة مخصصة:
1. ضع ملف `icon.ico` في `electron/assets/`
2. أعد بناء التطبيق
3. ستظهر الأيقونة الجديدة

### تغيير اسم التطبيق:
```json
// في electron/package.json
{
  "build": {
    "productName": "اسم التطبيق الجديد"
  }
}
```

## 📋 متطلبات التشغيل

### للتطوير:
- Node.js 16+
- npm
- Windows 10/11

### للمستخدم النهائي:
- Windows 10/11
- 4GB RAM
- 500MB مساحة فارغة

## 🚚 التوزيع

### للاستخدام الداخلي:
1. انسخ ملف `VendorsContracts-Portable-1.0.0.exe`
2. وزعه عبر USB أو الشبكة
3. المستخدمون يشغلونه مباشرة

### للتثبيت الاحترافي:
1. استخدم `VendorsContracts Setup 1.0.0.exe`
2. المستخدمون يثبتونه كبرنامج عادي
3. يظهر في قائمة البرامج

## ⚠️ استكشاف الأخطاء

### مشكلة: فشل البناء
```bash
# احذف node_modules وأعد التثبيت
cd electron
rmdir /s node_modules
npm install
npm run dist-win
```

### مشكلة: التطبيق لا يبدأ
- تحقق من وجود ملفات `client/build/`
- تحقق من وجود ملفات `server/dist/`
- أعد بناء المشروع كاملاً

### مشكلة: الأيقونة لا تظهر
- تأكد من وجود `electron/assets/icon.ico`
- أعد بناء التطبيق
- تحقق من مواصفات ملف ICO

## 📞 الدعم

### للمطورين:
- راجع `DESKTOP_APP_GUIDE.md` للتفاصيل الكاملة
- راجع `electron/` للكود المصدري

### للمستخدمين:
- التطبيق يعمل مثل أي برنامج Windows
- استخدم القوائم والاختصارات العادية
- البيانات محفوظة محلياً

## ✅ قائمة التحقق

- [ ] تم تثبيت Node.js
- [ ] تم تثبيت تبعيات Electron
- [ ] تم بناء العميل والخادم
- [ ] تم بناء تطبيق سطح المكتب
- [ ] تم اختبار التطبيق
- [ ] التطبيق جاهز للتوزيع

🎉 **تطبيق سطح المكتب جاهز!**
