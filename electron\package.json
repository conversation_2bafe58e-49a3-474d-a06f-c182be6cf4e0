{"name": "vendors-contracts-desktop", "version": "1.0.0", "description": "نظام إدارة الموردين والعقود - تطبيق سطح المكتب", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run server\" \"npm run client\" \"wait-on http://localhost:3000 && electron .\"", "server": "cd ../server && npm run dev", "client": "cd ../client && npm start", "build": "npm run build-client && npm run build-server && electron-builder", "build-client": "cd ../client && npm run build", "build-server": "cd ../server && npm run build", "pack": "electron-builder --dir", "dist": "electron-builder", "dist-win": "electron-builder --win", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.vendorscontracts.desktop", "productName": "نظام إدارة الموردين والعقود", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "assets/**/*", "../client/build/**/*", "../server/dist/**/*", "../server/node_modules/**/*", "../database/**/*"], "extraResources": [{"from": "../server/node_modules", "to": "server/node_modules"}, {"from": "../database", "to": "database"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة الموردين والعقود", "include": "installer.nsh"}, "portable": {"artifactName": "VendorsContracts-Portable-${version}.${ext}"}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "concurrently": "^8.2.2", "wait-on": "^7.0.1"}, "dependencies": {"electron-serve": "^1.1.0", "electron-store": "^8.1.0"}}