@echo off
echo ========================================
echo    نظام إدارة الموردين والعقود
echo    Vendors and Contracts Management
echo ========================================
echo.

echo [1/4] تحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo    تحميل من: https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js مثبت

echo.
echo [2/4] تثبيت التبعيات...
echo تثبيت تبعيات المشروع الرئيسي...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات المشروع الرئيسي
    pause
    exit /b 1
)

echo تثبيت تبعيات الخادم...
cd server
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات الخادم
    pause
    exit /b 1
)

echo تثبيت تبعيات العميل...
cd ..\client
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات العميل
    pause
    exit /b 1
)

echo تثبيت تبعيات تطبيق سطح المكتب...
cd ..\electron
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت تبعيات Electron
    pause
    exit /b 1
)

cd ..
echo ✅ تم تثبيت جميع التبعيات بنجاح

echo.
echo [3/4] إنشاء مجلدات النظام...
if not exist "database" mkdir database
if not exist "server\uploads" mkdir server\uploads
echo ✅ تم إنشاء المجلدات

echo.
echo [4/4] تشغيل النظام...
echo.
echo 🚀 بدء تشغيل النظام...
echo.
echo اختر نوع التشغيل:
echo   1. تطبيق الويب (متصفح)
echo   2. تطبيق سطح المكتب (EXE)
echo.
set /p choice="اختر (1 أو 2): "

if "%choice%"=="1" (
    echo.
    echo 🌐 تشغيل تطبيق الويب...
    echo سيتم فتح:
    echo   - الخادم على: http://localhost:5000
    echo   - التطبيق على: http://localhost:3000
    echo.
    start "خادم النظام" cmd /k "cd server && npm run dev"
    timeout /t 5 /nobreak >nul
    start "تطبيق النظام" cmd /k "cd client && npm start"
    echo.
    echo ✅ تم تشغيل تطبيق الويب!
    echo للوصول: http://localhost:3000
) else if "%choice%"=="2" (
    echo.
    echo 🖥️ تشغيل تطبيق سطح المكتب...
    cd electron
    call npm run dev
    echo.
    echo ✅ تم إغلاق تطبيق سطح المكتب
) else (
    echo خيار غير صحيح، سيتم تشغيل تطبيق الويب افتراضياً...
    start "خادم النظام" cmd /k "cd server && npm run dev"
    timeout /t 5 /nobreak >nul
    start "تطبيق النظام" cmd /k "cd client && npm start"
    echo ✅ تم تشغيل تطبيق الويب!
)
echo.
echo للمساعدة:
echo   📖 اقرأ ملف INSTALLATION.md
echo   🧪 اقرأ ملف TEST_GUIDE.md
echo.
pause
