import { Request, Response } from 'express';
import { VendorModel } from '../models/Vendor';
import { ApiResponse, Vendor } from '../types';

export class VendorController {
  // إنشاء مورد جديد
  static async create(req: Request, res: Response) {
    try {
      const vendorData: Omit<Vendor, 'id' | 'created_at' | 'updated_at'> = req.body;
      
      const vendorId = await VendorModel.create(vendorData);
      const vendor = await VendorModel.getById(vendorId);

      const response: ApiResponse<Vendor> = {
        success: true,
        data: vendor!,
        message: 'تم إنشاء المورد بنجاح'
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('خطأ في إنشاء المورد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في إنشاء المورد'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على جميع الموردين
  static async getAll(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string || '';
      const sortBy = req.query.sortBy as string || 'name';
      const sortOrder = (req.query.sortOrder as string) || 'ASC';

      const result = await VendorModel.getAll({
        page,
        limit,
        search,
        sortBy,
        sortOrder: sortOrder as 'ASC' | 'DESC'
      });

      const response: ApiResponse<typeof result> = {
        success: true,
        data: result
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب الموردين:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب الموردين'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على مورد بالمعرف
  static async getById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const vendor = await VendorModel.getById(id);

      if (!vendor) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'المورد غير موجود'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse<Vendor> = {
        success: true,
        data: vendor
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب المورد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب المورد'
      };
      res.status(500).json(response);
    }
  }

  // تحديث مورد
  static async update(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const vendorData: Partial<Vendor> = req.body;

      const updated = await VendorModel.update(id, vendorData);

      if (!updated) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'المورد غير موجود أو لم يتم التحديث'
        };
        return res.status(404).json(response);
      }

      const vendor = await VendorModel.getById(id);
      const response: ApiResponse<Vendor> = {
        success: true,
        data: vendor!,
        message: 'تم تحديث المورد بنجاح'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في تحديث المورد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في تحديث المورد'
      };
      res.status(500).json(response);
    }
  }

  // حذف مورد
  static async delete(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const deleted = await VendorModel.delete(id);

      if (!deleted) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'المورد غير موجود'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse<null> = {
        success: true,
        message: 'تم حذف المورد بنجاح'
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في حذف المورد:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في حذف المورد'
      };
      res.status(500).json(response);
    }
  }

  // الحصول على الموردين النشطين
  static async getActive(req: Request, res: Response) {
    try {
      const vendors = await VendorModel.getActive();

      const response: ApiResponse<Vendor[]> = {
        success: true,
        data: vendors
      };

      res.json(response);
    } catch (error) {
      console.error('خطأ في جلب الموردين النشطين:', error);
      const response: ApiResponse<null> = {
        success: false,
        error: 'حدث خطأ في جلب الموردين النشطين'
      };
      res.status(500).json(response);
    }
  }
}
