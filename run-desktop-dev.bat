@echo off
echo ========================================
echo    تشغيل تطبيق سطح المكتب - وضع التطوير
echo    Running Desktop App - Development Mode
echo ========================================
echo.

echo [1/3] تحقق من التبعيات...
if not exist "electron\node_modules" (
    echo تثبيت تبعيات Electron...
    cd electron
    call npm install
    cd ..
)
echo ✅ التبعيات متوفرة

echo.
echo [2/3] تحقق من بناء المشروع...
if not exist "client\build" (
    echo بناء العميل...
    cd client
    call npm run build
    cd ..
)
if not exist "server\dist" (
    echo بناء الخادم...
    cd server
    call npm run build
    cd ..
)
echo ✅ المشروع جاهز

echo.
echo [3/3] تشغيل التطبيق...
echo.
echo 🖥️ سيتم فتح تطبيق سطح المكتب...
echo.
cd electron
set NODE_ENV=development
call npm run dev

echo.
echo تم إغلاق التطبيق
pause
