# دليل الأيقونات
## Icons Guide

### 📁 الملفات المطلوبة

ضع الملفات التالية في هذا المجلد:

#### للتطبيق:
- `icon.ico` - أيقونة Windows (256x256, 128x128, 64x64, 32x32, 16x16)
- `icon.png` - أيقونة عامة (512x512 PNG)
- `logo.png` - شعار التطبيق (256x256 PNG)

#### للمثبت:
- `installer-icon.ico` - أيقونة المثبت
- `installer-header.bmp` - رأس المثبت (150x57)
- `installer-sidebar.bmp` - جانب المثبت (164x314)

### 🎨 إنشاء الأيقونات

#### باستخدام أدوات مجانية:

1. **GIMP** (مجاني):
   - إنشاء تصميم 512x512
   - تصدير كـ PNG للأيقونة العامة
   - استخدام plugin لتصدير ICO

2. **Paint.NET** (مجاني):
   - مع plugin ICO format
   - إنشاء أحجام متعددة

3. **Online ICO Converter**:
   - رفع PNG وتحويل لـ ICO
   - مواقع مثل: favicon.io, convertio.co

#### التصميم المقترح:

```
📊 أيقونة بسيطة تحتوي على:
- رمز مجلد أو ملف
- رمز عقد أو توقيع
- ألوان: أزرق وأخضر
- خلفية شفافة أو بيضاء
```

### 🔧 إعداد الأيقونات في Electron

#### في package.json:
```json
{
  "build": {
    "win": {
      "icon": "assets/icon.ico"
    },
    "mac": {
      "icon": "assets/icon.icns"
    },
    "linux": {
      "icon": "assets/icon.png"
    }
  }
}
```

#### في main.js:
```javascript
new BrowserWindow({
  icon: path.join(__dirname, 'assets', 'icon.png')
});
```

### 📐 مواصفات الأيقونات

#### Windows ICO:
- أحجام: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256
- تنسيق: ICO مع أحجام متعددة
- عمق الألوان: 32-bit مع شفافية

#### PNG العامة:
- حجم: 512x512 أو 1024x1024
- تنسيق: PNG مع شفافية
- جودة عالية للتكبير

### 🎯 نصائح التصميم

1. **البساطة**: تصميم واضح وبسيط
2. **التباين**: ألوان واضحة ومتباينة
3. **القابلية للقراءة**: واضح في الأحجام الصغيرة
4. **الاتساق**: متناسق مع هوية التطبيق

### 🔄 تحديث الأيقونات

بعد إضافة الأيقونات:

```bash
# إعادة بناء التطبيق
cd electron
npm run dist-win
```

### 📝 ملاحظات

- تأكد من وجود جميع الأحجام في ملف ICO
- اختبر الأيقونة في أحجام مختلفة
- تأكد من وضوح الأيقونة على خلفيات مختلفة
- احفظ نسخة احتياطية من الملفات المصدرية

### 🆘 إذا لم تتوفر أيقونات مخصصة

سيستخدم Electron الأيقونة الافتراضية، ولكن يُنصح بشدة بإنشاء أيقونة مخصصة لإضفاء الطابع المهني على التطبيق.
