import { db } from '../config/database';
import { Contract, PaginationParams, PaginatedResponse } from '../types';

export class ContractModel {
  // إنشاء عقد جديد
  static create(contract: Omit<Contract, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO contracts (
          contract_number, vendor_id, contract_type_id, title, description,
          start_date, end_date, value, currency, payment_terms, delivery_terms,
          discount_percentage, status, auto_renewal, renewal_period, 
          notification_days, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const values = [
        contract.contract_number,
        contract.vendor_id,
        contract.contract_type_id,
        contract.title,
        contract.description || null,
        contract.start_date,
        contract.end_date,
        contract.value || null,
        contract.currency || 'ريال سعودي',
        contract.payment_terms || null,
        contract.delivery_terms || null,
        contract.discount_percentage || null,
        contract.status || 'نشط',
        contract.auto_renewal || false,
        contract.renewal_period || null,
        contract.notification_days || 30,
        contract.notes || null
      ];

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.lastID);
        }
      });
    });
  }

  // الحصول على جميع العقود مع بيانات المورد ونوع العقد
  static getAll(params: PaginationParams = {}): Promise<PaginatedResponse<Contract>> {
    return new Promise((resolve, reject) => {
      const { page = 1, limit = 10, search = '', sortBy = 'created_at', sortOrder = 'DESC' } = params;
      const offset = (page - 1) * limit;

      let whereClause = '';
      let searchParams: any[] = [];

      if (search) {
        whereClause = `
          WHERE c.contract_number LIKE ? OR c.title LIKE ? 
          OR v.name LIKE ? OR ct.name LIKE ?
        `;
        const searchTerm = `%${search}%`;
        searchParams = [searchTerm, searchTerm, searchTerm, searchTerm];
      }

      // الحصول على العدد الإجمالي
      const countSql = `
        SELECT COUNT(*) as total 
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
        ${whereClause}
      `;
      
      db.get(countSql, searchParams, (err, countResult: any) => {
        if (err) {
          reject(err);
          return;
        }

        const total = countResult.total;
        const totalPages = Math.ceil(total / limit);

        // الحصول على البيانات
        const dataSql = `
          SELECT 
            c.*,
            v.name as vendor_name,
            v.contact_person as vendor_contact,
            ct.name as contract_type_name
          FROM contracts c
          LEFT JOIN vendors v ON c.vendor_id = v.id
          LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
          ${whereClause}
          ORDER BY c.${sortBy} ${sortOrder}
          LIMIT ? OFFSET ?
        `;

        db.all(dataSql, [...searchParams, limit, offset], (err, rows: any[]) => {
          if (err) {
            reject(err);
          } else {
            const contracts = rows.map(row => ({
              ...row,
              vendor: {
                id: row.vendor_id,
                name: row.vendor_name,
                contact_person: row.vendor_contact
              },
              contract_type: {
                id: row.contract_type_id,
                name: row.contract_type_name
              }
            }));

            resolve({
              data: contracts,
              total,
              page,
              limit,
              totalPages
            });
          }
        });
      });
    });
  }

  // الحصول على عقد بالمعرف
  static getById(id: number): Promise<Contract | null> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          c.*,
          v.name as vendor_name,
          v.contact_person as vendor_contact,
          v.phone as vendor_phone,
          v.email as vendor_email,
          ct.name as contract_type_name,
          ct.description as contract_type_description
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
        WHERE c.id = ?
      `;
      
      db.get(sql, [id], (err, row: any) => {
        if (err) {
          reject(err);
        } else if (row) {
          const contract = {
            ...row,
            vendor: {
              id: row.vendor_id,
              name: row.vendor_name,
              contact_person: row.vendor_contact,
              phone: row.vendor_phone,
              email: row.vendor_email
            },
            contract_type: {
              id: row.contract_type_id,
              name: row.contract_type_name,
              description: row.contract_type_description
            }
          };
          resolve(contract);
        } else {
          resolve(null);
        }
      });
    });
  }

  // تحديث عقد
  static update(id: number, contract: Partial<Contract>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const fields = Object.keys(contract).filter(key => 
        key !== 'id' && key !== 'created_at' && key !== 'vendor' && key !== 'contract_type'
      );
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const values = fields.map(field => contract[field as keyof Contract]);

      const sql = `
        UPDATE contracts 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      db.run(sql, [...values, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // حذف عقد
  static delete(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM contracts WHERE id = ?';
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // الحصول على العقود المنتهية الصلاحية قريباً
  static getExpiringContracts(days: number = 30): Promise<Contract[]> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          c.*,
          v.name as vendor_name,
          ct.name as contract_type_name
        FROM contracts c
        LEFT JOIN vendors v ON c.vendor_id = v.id
        LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
        WHERE c.status = 'نشط' 
        AND DATE(c.end_date) <= DATE('now', '+' || ? || ' days')
        ORDER BY c.end_date ASC
      `;
      
      db.all(sql, [days], (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const contracts = rows.map(row => ({
            ...row,
            vendor: { name: row.vendor_name },
            contract_type: { name: row.contract_type_name }
          }));
          resolve(contracts);
        }
      });
    });
  }

  // إنشاء رقم عقد تلقائي
  static generateContractNumber(contractTypeId: number): Promise<string> {
    return new Promise((resolve, reject) => {
      const year = new Date().getFullYear();
      const sql = `
        SELECT COUNT(*) as count 
        FROM contracts 
        WHERE contract_type_id = ? 
        AND strftime('%Y', created_at) = ?
      `;
      
      db.get(sql, [contractTypeId, year.toString()], (err, result: any) => {
        if (err) {
          reject(err);
        } else {
          const count = result.count + 1;
          const typePrefix = contractTypeId === 1 ? 'SUP' : contractTypeId === 2 ? 'RNT' : 'DSC';
          const contractNumber = `${typePrefix}-${year}-${count.toString().padStart(4, '0')}`;
          resolve(contractNumber);
        }
      });
    });
  }
}
