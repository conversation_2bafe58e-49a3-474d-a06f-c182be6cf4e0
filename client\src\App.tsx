import React, { useState } from 'react';
import {
  ThemeProvider,
  CssBaseline,
  Box,
  AppBar,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Container,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Badge,
  useMediaQuery,
  useTheme as useMuiTheme,
} from '@mui/material';
import {
  Menu as MenuIcon,
  People as PeopleIcon,
  Description as ContractIcon,
  Folder as DocumentIcon,
  Notifications as NotificationIcon,
  Dashboard as DashboardIcon,
} from '@mui/icons-material';
import { theme } from './theme';
import VendorList from './components/VendorList';
import VendorForm from './components/VendorForm';
import ContractList from './components/ContractList';
import NotificationList from './components/NotificationList';
import DocumentList from './components/DocumentList';
import Dashboard from './components/Dashboard';
import { Vendor, Contract } from './types';

// عرض الصفحات المختلفة
type PageView = 'dashboard' | 'vendors' | 'contracts' | 'documents' | 'notifications';

function App() {
  const [currentPage, setCurrentPage] = useState<PageView>('dashboard');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [vendorFormOpen, setVendorFormOpen] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [refreshVendors, setRefreshVendors] = useState(0);
  const [refreshContracts, setRefreshContracts] = useState(0);

  const muiTheme = useMuiTheme();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('md'));

  // قائمة التنقل
  const navigationItems = [
    { id: 'dashboard', label: 'لوحة التحكم', icon: <DashboardIcon /> },
    { id: 'vendors', label: 'الموردين', icon: <PeopleIcon /> },
    { id: 'contracts', label: 'العقود', icon: <ContractIcon /> },
    { id: 'documents', label: 'المستندات', icon: <DocumentIcon /> },
    { id: 'notifications', label: 'التنبيهات', icon: <NotificationIcon /> },
  ];

  // معالج فتح نموذج المورد للإضافة
  const handleAddVendor = () => {
    setSelectedVendor(null);
    setVendorFormOpen(true);
  };

  // معالج فتح نموذج المورد للتعديل
  const handleEditVendor = (vendor: Vendor) => {
    setSelectedVendor(vendor);
    setVendorFormOpen(true);
  };

  // معالج عرض تفاصيل المورد
  const handleViewVendor = (vendor: Vendor) => {
    // TODO: إضافة صفحة عرض تفاصيل المورد
    console.log('View vendor:', vendor);
  };

  // معالج نجاح العملية
  const handleVendorFormSuccess = () => {
    setRefreshVendors(prev => prev + 1);
  };

  // معالجات العقود
  const handleAddContract = () => {
    // TODO: إضافة نموذج العقد
    console.log('Add contract');
  };

  const handleEditContract = (contract: Contract) => {
    // TODO: تعديل العقد
    console.log('Edit contract:', contract);
  };

  const handleViewContract = (contract: Contract) => {
    // TODO: عرض تفاصيل العقد
    console.log('View contract:', contract);
  };

  // عرض المحتوى حسب الصفحة المحددة
  const renderPageContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;

      case 'vendors':
        return (
          <VendorList
            key={refreshVendors}
            onAdd={handleAddVendor}
            onEdit={handleEditVendor}
            onView={handleViewVendor}
          />
        );

      case 'contracts':
        return (
          <ContractList
            key={refreshContracts}
            onAdd={handleAddContract}
            onEdit={handleEditContract}
            onView={handleViewContract}
          />
        );

      case 'documents':
        return <DocumentList />;

      case 'notifications':
        return <NotificationList />;

      default:
        return null;
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex' }}>
        {/* شريط التطبيق العلوي */}
        <AppBar
          position="fixed"
          sx={{
            zIndex: (theme) => theme.zIndex.drawer + 1,
            backgroundColor: 'primary.main',
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              edge="start"
              onClick={() => setDrawerOpen(!drawerOpen)}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              نظام إدارة الموردين والعقود
            </Typography>

            <IconButton color="inherit">
              <Badge badgeContent={4} color="error">
                <NotificationIcon />
              </Badge>
            </IconButton>
          </Toolbar>
        </AppBar>

        {/* القائمة الجانبية */}
        <Drawer
          variant={isMobile ? 'temporary' : 'persistent'}
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
          sx={{
            width: 240,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: 240,
              boxSizing: 'border-box',
            },
          }}
        >
          <Toolbar />
          <Box sx={{ overflow: 'auto' }}>
            <List>
              {navigationItems.map((item) => (
                <ListItem
                  key={item.id}
                  button
                  selected={currentPage === item.id}
                  onClick={() => {
                    setCurrentPage(item.id as PageView);
                    if (isMobile) setDrawerOpen(false);
                  }}
                >
                  <ListItemIcon>{item.icon}</ListItemIcon>
                  <ListItemText primary={item.label} />
                </ListItem>
              ))}
            </List>
          </Box>
        </Drawer>

        {/* المحتوى الرئيسي */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            width: { sm: `calc(100% - ${drawerOpen ? 240 : 0}px)` },
            transition: (theme) =>
              theme.transitions.create(['margin', 'width'], {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.leavingScreen,
              }),
          }}
        >
          <Toolbar />
          <Container maxWidth="xl">
            {renderPageContent()}
          </Container>
        </Box>
      </Box>

      {/* نموذج المورد */}
      <VendorForm
        open={vendorFormOpen}
        onClose={() => setVendorFormOpen(false)}
        onSuccess={handleVendorFormSuccess}
        vendor={selectedVendor}
      />
    </ThemeProvider>
  );
}

export default App;
