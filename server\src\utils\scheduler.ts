import cron from 'node-cron';
import { NotificationModel } from '../models/Notification';

// تشغيل مهمة إنشاء تنبيهات انتهاء العقود يومياً في الساعة 9 صباحاً
export const startNotificationScheduler = () => {
  console.log('🕘 تم تشغيل مجدول التنبيهات');

  // تشغيل يومياً في الساعة 9:00 صباحاً
  cron.schedule('0 9 * * *', async () => {
    try {
      console.log('⏰ تشغيل مهمة إنشاء تنبيهات انتهاء العقود...');
      
      const createdCount = await NotificationModel.createExpiryNotifications();
      
      if (createdCount > 0) {
        console.log(`✅ تم إنشاء ${createdCount} تنبيه جديد لانتهاء العقود`);
      } else {
        console.log('ℹ️ لا توجد عقود تحتاج تنبيهات جديدة');
      }
    } catch (error) {
      console.error('❌ خطأ في مهمة إنشاء التنبيهات:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Riyadh"
  });

  // تشغيل مهمة تنظيف التنبيهات القديمة أسبوعياً يوم الأحد في الساعة 2 صباحاً
  cron.schedule('0 2 * * 0', async () => {
    try {
      console.log('🧹 تشغيل مهمة تنظيف التنبيهات القديمة...');
      
      // حذف التنبيهات المقروءة التي مر عليها أكثر من 30 يوم
      // TODO: إضافة دالة تنظيف في NotificationModel
      
      console.log('✅ تم تنظيف التنبيهات القديمة');
    } catch (error) {
      console.error('❌ خطأ في مهمة تنظيف التنبيهات:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Riyadh"
  });

  console.log('📅 تم جدولة المهام التالية:');
  console.log('  - إنشاء تنبيهات انتهاء العقود: يومياً في 9:00 ص');
  console.log('  - تنظيف التنبيهات القديمة: أسبوعياً يوم الأحد في 2:00 ص');
};

// إيقاف جميع المهام المجدولة
export const stopScheduler = () => {
  cron.getTasks().forEach(task => {
    task.stop();
  });
  console.log('⏹️ تم إيقاف جميع المهام المجدولة');
};

// تشغيل مهمة إنشاء التنبيهات فوراً (للاختبار)
export const runNotificationTaskNow = async () => {
  try {
    console.log('🔄 تشغيل مهمة التنبيهات فوراً...');
    
    const createdCount = await NotificationModel.createExpiryNotifications();
    
    console.log(`✅ تم إنشاء ${createdCount} تنبيه جديد`);
    return createdCount;
  } catch (error) {
    console.error('❌ خطأ في تشغيل مهمة التنبيهات:', error);
    throw error;
  }
};
