import { Router } from 'express';
import { VendorController } from '../controllers/vendorController';

const router = Router();

// مسارات الموردين
router.post('/', VendorController.create);           // إنشاء مورد جديد
router.get('/', VendorController.getAll);            // الحصول على جميع الموردين
router.get('/active', VendorController.getActive);   // الحصول على الموردين النشطين
router.get('/:id', VendorController.getById);        // الحصول على مورد بالمعرف
router.put('/:id', VendorController.update);         // تحديث مورد
router.delete('/:id', VendorController.delete);      // حذف مورد

export default router;
