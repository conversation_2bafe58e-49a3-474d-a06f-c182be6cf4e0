import { Router } from 'express';
import { ContractController } from '../controllers/contractController';

const router = Router();

// مسارات العقود
router.post('/', ContractController.create);                    // إنشاء عقد جديد
router.get('/', ContractController.getAll);                     // الحصول على جميع العقود
router.get('/expiring', ContractController.getExpiring);        // الحصول على العقود المنتهية قريباً
router.post('/generate-number', ContractController.generateNumber); // إنشاء رقم عقد تلقائي
router.get('/:id', ContractController.getById);                 // الحصول على عقد بالمعرف
router.put('/:id', ContractController.update);                  // تحديث عقد
router.delete('/:id', ContractController.delete);               // حذف عقد

export default router;
