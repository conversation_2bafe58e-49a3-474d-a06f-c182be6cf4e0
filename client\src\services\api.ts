import axios from 'axios';
import { 
  Vendor, 
  Contract, 
  Document, 
  Notification, 
  ApiResponse, 
  PaginatedResponse,
  PaginationParams,
  VendorFormData,
  ContractFormData 
} from '../types';

// إعداد axios
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// معالج الاستجابات
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// خدمات الموردين
export const vendorService = {
  // إنشاء مورد جديد
  create: async (vendorData: VendorFormData): Promise<ApiResponse<Vendor>> => {
    const response = await api.post('/vendors', vendorData);
    return response.data;
  },

  // الحصول على جميع الموردين
  getAll: async (params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Vendor>>> => {
    const response = await api.get('/vendors', { params });
    return response.data;
  },

  // الحصول على مورد بالمعرف
  getById: async (id: number): Promise<ApiResponse<Vendor>> => {
    const response = await api.get(`/vendors/${id}`);
    return response.data;
  },

  // تحديث مورد
  update: async (id: number, vendorData: Partial<VendorFormData>): Promise<ApiResponse<Vendor>> => {
    const response = await api.put(`/vendors/${id}`, vendorData);
    return response.data;
  },

  // حذف مورد
  delete: async (id: number): Promise<ApiResponse<null>> => {
    const response = await api.delete(`/vendors/${id}`);
    return response.data;
  },

  // الحصول على الموردين النشطين
  getActive: async (): Promise<ApiResponse<Vendor[]>> => {
    const response = await api.get('/vendors/active');
    return response.data;
  },
};

// خدمات العقود
export const contractService = {
  // إنشاء عقد جديد
  create: async (contractData: ContractFormData): Promise<ApiResponse<Contract>> => {
    const response = await api.post('/contracts', contractData);
    return response.data;
  },

  // الحصول على جميع العقود
  getAll: async (params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Contract>>> => {
    const response = await api.get('/contracts', { params });
    return response.data;
  },

  // الحصول على عقد بالمعرف
  getById: async (id: number): Promise<ApiResponse<Contract>> => {
    const response = await api.get(`/contracts/${id}`);
    return response.data;
  },

  // تحديث عقد
  update: async (id: number, contractData: Partial<ContractFormData>): Promise<ApiResponse<Contract>> => {
    const response = await api.put(`/contracts/${id}`, contractData);
    return response.data;
  },

  // حذف عقد
  delete: async (id: number): Promise<ApiResponse<null>> => {
    const response = await api.delete(`/contracts/${id}`);
    return response.data;
  },

  // الحصول على العقود المنتهية قريباً
  getExpiring: async (days?: number): Promise<ApiResponse<Contract[]>> => {
    const response = await api.get('/contracts/expiring', { params: { days } });
    return response.data;
  },

  // إنشاء رقم عقد تلقائي
  generateNumber: async (contractTypeId: number): Promise<ApiResponse<{ contractNumber: string }>> => {
    const response = await api.post('/contracts/generate-number', { contractTypeId });
    return response.data;
  },
};

// خدمات المستندات
export const documentService = {
  // رفع مستند
  upload: async (file: File, contractId?: number, vendorId?: number, documentType?: string): Promise<ApiResponse<Document>> => {
    const formData = new FormData();
    formData.append('file', file);
    if (contractId) formData.append('contractId', contractId.toString());
    if (vendorId) formData.append('vendorId', vendorId.toString());
    if (documentType) formData.append('documentType', documentType);

    const response = await api.post('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // الحصول على مستندات العقد
  getByContract: async (contractId: number): Promise<ApiResponse<Document[]>> => {
    const response = await api.get(`/documents/contract/${contractId}`);
    return response.data;
  },

  // الحصول على مستندات المورد
  getByVendor: async (vendorId: number): Promise<ApiResponse<Document[]>> => {
    const response = await api.get(`/documents/vendor/${vendorId}`);
    return response.data;
  },

  // حذف مستند
  delete: async (id: number): Promise<ApiResponse<null>> => {
    const response = await api.delete(`/documents/${id}`);
    return response.data;
  },

  // تحميل مستند
  download: async (id: number): Promise<Blob> => {
    const response = await api.get(`/documents/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// خدمات التنبيهات
export const notificationService = {
  // الحصول على جميع التنبيهات
  getAll: async (): Promise<ApiResponse<Notification[]>> => {
    const response = await api.get('/notifications');
    return response.data;
  },

  // الحصول على التنبيهات غير المقروءة
  getUnread: async (): Promise<ApiResponse<Notification[]>> => {
    const response = await api.get('/notifications/unread');
    return response.data;
  },

  // تحديد تنبيه كمقروء
  markAsRead: async (id: number): Promise<ApiResponse<null>> => {
    const response = await api.put(`/notifications/${id}/read`);
    return response.data;
  },

  // تحديد جميع التنبيهات كمقروءة
  markAllAsRead: async (): Promise<ApiResponse<null>> => {
    const response = await api.put('/notifications/read-all');
    return response.data;
  },

  // حذف تنبيه
  delete: async (id: number): Promise<ApiResponse<null>> => {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  },
};

// خدمة فحص صحة الخادم
export const healthService = {
  check: async (): Promise<ApiResponse<any>> => {
    const response = await api.get('/health');
    return response.data;
  },
};

export default api;
