{"name": "vendors-contracts-client", "version": "1.0.0", "description": "واجهة نظام إدارة الموردين والعقود", "private": true, "dependencies": {"@mui/material": "^5.14.5", "@mui/icons-material": "^5.14.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-date-pickers": "^6.10.2", "@mui/x-data-grid": "^6.10.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "axios": "^1.4.0", "moment": "^2.29.4", "react-hook-form": "^7.45.2", "@hookform/resolvers": "^3.1.1", "yup": "^1.2.0", "react-dropzone": "^14.2.3", "notistack": "^3.0.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/node": "^20.4.5", "typescript": "^5.1.6", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}