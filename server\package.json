{"name": "vendors-contracts-server", "version": "1.0.0", "description": "خادم نظام إدارة الموردين والعقود", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "moment": "^2.29.4", "node-cron": "^3.0.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.4.5", "@types/node-cron": "^3.0.8", "typescript": "^5.1.6", "ts-node-dev": "^2.0.0", "jest": "^29.6.2", "@types/jest": "^29.5.3"}}