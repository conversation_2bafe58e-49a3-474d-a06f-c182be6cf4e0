const { contextBridge, ipc<PERSON>enderer } = require('electron');

// تعريض APIs آمنة للعميل
contextBridge.exposeInMainWorld('electronAPI', {
  // معلومات التطبيق
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // حوارات الملفات
  showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),
  showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
  
  // معلومات النظام
  platform: process.platform,
  isElectron: true,
  
  // إشعارات النظام
  showNotification: (title, body) => {
    if (Notification.permission === 'granted') {
      new Notification(title, { body });
    }
  },
  
  // طلب إذن الإشعارات
  requestNotificationPermission: async () => {
    return await Notification.requestPermission();
  }
});

// إعداد الإشعارات
window.addEventListener('DOMContentLoaded', () => {
  // طلب إذن الإشعارات
  if (Notification.permission === 'default') {
    Notification.requestPermission();
  }
});
