import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import path from 'path';
import { initializeDatabase } from './config/database';

// استيراد المسارات
import vendorRoutes from './routes/vendorRoutes';
import contractRoutes from './routes/contractRoutes';
import notificationRoutes from './routes/notificationRoutes';
import documentRoutes from './routes/documentRoutes';

// استيراد المجدول
import { startNotificationScheduler } from './utils/scheduler';

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد الوسطاء (Middleware)
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// إعداد مجلد الملفات الثابتة للمستندات
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// إعداد المسارات
app.use('/api/vendors', vendorRoutes);
app.use('/api/contracts', contractRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/documents', documentRoutes);

// مسار الصحة
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'الخادم يعمل بشكل طبيعي',
    timestamp: new Date().toISOString()
  });
});

// مسار افتراضي
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في نظام إدارة الموردين والعقود',
    version: '1.0.0',
    endpoints: {
      vendors: '/api/vendors',
      contracts: '/api/contracts',
      documents: '/api/documents',
      notifications: '/api/notifications'
    }
  });
});

// معالج الأخطاء العام
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('خطأ في الخادم:', err);
  res.status(500).json({
    success: false,
    error: 'حدث خطأ داخلي في الخادم'
  });
});

// معالج المسارات غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'المسار غير موجود'
  });
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`📊 لوحة التحكم: http://localhost:${PORT}`);
  console.log(`🔗 API: http://localhost:${PORT}/api`);

  // تهيئة قاعدة البيانات
  initializeDatabase();

  // تشغيل مجدول التنبيهات
  startNotificationScheduler();
});

export default app;
