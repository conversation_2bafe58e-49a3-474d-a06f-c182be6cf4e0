# دليل التثبيت والتشغيل
## نظام إدارة الموردين والعقود

### المتطلبات الأساسية

قبل البدء، تأكد من تثبيت البرامج التالية:

- **Node.js** (الإصدار 16 أو أحدث)
- **npm** أو **yarn**
- **Git** (اختياري)

### خطوات التثبيت

#### 1. تحميل المشروع

```bash
# إذا كان لديك Git
git clone <repository-url>
cd my-coop

# أو قم بتحميل الملفات مباشرة
```

#### 2. تثبيت التبعيات

```bash
# تثبيت جميع التبعيات للمشروع والخادم والعميل
npm run install-all
```

أو يمكنك تثبيت كل جزء منفصلاً:

```bash
# تثبيت تبعيات المشروع الرئيسي
npm install

# تثبيت تبعيات الخادم
cd server
npm install

# تثبيت تبعيات العميل
cd ../client
npm install
```

#### 3. إعداد قاعدة البيانات

قاعدة البيانات SQLite ستُنشأ تلقائياً عند تشغيل الخادم لأول مرة في مجلد `database/`.

#### 4. تشغيل المشروع

```bash
# العودة للمجلد الرئيسي
cd ..

# تشغيل الخادم والعميل معاً
npm run dev
```

أو يمكنك تشغيل كل جزء منفصلاً:

```bash
# تشغيل الخادم فقط (في terminal منفصل)
cd server
npm run dev

# تشغيل العميل فقط (في terminal آخر)
cd client
npm start
```

### الوصول للتطبيق

بعد التشغيل الناجح:

- **الواجهة الأمامية**: http://localhost:3000
- **API الخادم**: http://localhost:5000
- **صحة الخادم**: http://localhost:5000/api/health

### هيكل المشروع

```
my-coop/
├── client/                 # تطبيق React
│   ├── src/
│   │   ├── components/     # مكونات React
│   │   ├── pages/          # صفحات التطبيق
│   │   ├── services/       # خدمات API
│   │   ├── types/          # أنواع TypeScript
│   │   ├── theme/          # إعدادات التصميم
│   │   └── utils/          # أدوات مساعدة
│   └── public/             # ملفات ثابتة
├── server/                 # خادم Node.js
│   ├── src/
│   │   ├── controllers/    # متحكمات API
│   │   ├── models/         # نماذج قاعدة البيانات
│   │   ├── routes/         # مسارات API
│   │   ├── middleware/     # وسطاء Express
│   │   ├── config/         # إعدادات قاعدة البيانات
│   │   └── utils/          # أدوات مساعدة
│   └── uploads/            # ملفات المستندات المرفوعة
├── database/               # ملفات قاعدة البيانات
└── docs/                   # الوثائق
```

### المميزات المتاحة

#### ✅ إدارة الموردين
- إضافة وتعديل وحذف الموردين
- البحث والفلترة
- تقييم الموردين
- إدارة معلومات الاتصال

#### ✅ إدارة العقود
- عقود التوريد
- عقود الإيجار
- اتفاقات الخصم
- تتبع تواريخ الانتهاء
- إنشاء أرقام عقود تلقائية

#### ✅ نظام التنبيهات
- تنبيهات انتهاء العقود
- تذكيرات التجديد
- إشعارات تلقائية يومية

#### ✅ إدارة المستندات
- رفع وتخزين المستندات
- ربط المستندات بالعقود والموردين
- تحميل المستندات
- دعم أنواع ملفات متعددة

#### ✅ لوحة التحكم
- إحصائيات شاملة
- مؤشرات الأداء
- العقود المنتهية قريباً
- نظرة عامة على النظام

### استكشاف الأخطاء

#### مشكلة في تشغيل الخادم

```bash
# تأكد من أن المنفذ 5000 غير مستخدم
netstat -an | findstr :5000

# أو غيّر المنفذ في متغيرات البيئة
set PORT=5001
npm run dev
```

#### مشكلة في تشغيل العميل

```bash
# تأكد من أن المنفذ 3000 غير مستخدم
netstat -an | findstr :3000

# أو غيّر المنفذ
set PORT=3001
npm start
```

#### مشكلة في قاعدة البيانات

```bash
# احذف قاعدة البيانات وأعد إنشاؤها
rm database/vendors_contracts.db
npm run dev
```

### التطوير والتخصيص

#### إضافة ميزات جديدة

1. **إضافة نموذج جديد**: أنشئ ملف في `server/src/models/`
2. **إضافة متحكم**: أنشئ ملف في `server/src/controllers/`
3. **إضافة مسارات**: أنشئ ملف في `server/src/routes/`
4. **إضافة مكون React**: أنشئ ملف في `client/src/components/`

#### متغيرات البيئة

أنشئ ملف `.env` في مجلد `server/`:

```env
PORT=5000
NODE_ENV=development
DB_PATH=../database/vendors_contracts.db
UPLOAD_PATH=./uploads
```

### النشر في الإنتاج

#### بناء التطبيق

```bash
# بناء العميل
cd client
npm run build

# بناء الخادم
cd ../server
npm run build
```

#### تشغيل الإنتاج

```bash
# تشغيل الخادم في وضع الإنتاج
cd server
npm start
```

### الدعم والمساعدة

إذا واجهت أي مشاكل:

1. تأكد من تثبيت جميع التبعيات
2. تحقق من إصدارات Node.js و npm
3. راجع رسائل الخطأ في وحدة التحكم
4. تأكد من عدم استخدام المنافذ المطلوبة

### الترخيص

هذا المشروع مرخص تحت رخصة MIT.
